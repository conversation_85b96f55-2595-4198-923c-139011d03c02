<?php
require_once('head.php');
$mod = 'blank';
$title = '质押中心';
if ($userrow['uuid'] != 1) {
    exit("<script language='javascript'>window.location.href='login.php';</script>");
}
?>
<div class="app-content-body" id="app">
    <el-container>
        <el-main>
            <!-- 质押方案卡片 -->
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>直属质押方案</span>
                </div>
                <div v-if="zhiyaList.length === 0" class="empty-text">
                    暂无可用的质押方案
                </div>
                <el-row :gutter="20">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="item in zhiyaList" :key="item.id">
                        <el-card shadow="hover" class="zhiya-card" :class="{'disabled': hasZhiya(item.id)}">
                            <div class="zhiya-title">{{item.category_name}}</div>
                            <div class="zhiya-amount">
                                <span class="label">质押</span>
                                <span class="value">{{item.amount}}</span>
                                <span class="unit">元</span>
                            </div>
                            <div class="zhiya-discount">
                                <span class="label">可享该分类</span>
                                <span class="value">{{(item.discount_rate * 10).toFixed(1)}}</span>
                                <span class="unit">折</span>
                            </div>
                            <div class="zhiya-discount">
                                <span class="label">质押期限</span>
                                <span class="value">{{item.days}}</span>
                                <span class="unit">天</span>
                            </div>
                            <div class="zhiya-action">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="handleZhiya(item)"
                                    :disabled="hasZhiya(item.id)">
                                    {{hasZhiya(item.id) ? '已质押' : '立即质押'}}
                                </el-button>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-card>
            <!-- 我的质押记录 -->
            <el-card class="box-card" style="margin-top: 20px">
                <div slot="header" class="clearfix">
                    <span>我的质押</span>
                </div>
                <el-table
                    :data="myZhiya.data"
                    stripe
                    v-loading="loading"
                    border
                    :row-class-name="tableRowClassName"
                    style="width: 100%">
                    <el-table-column prop="category_name" label="分类名称"></el-table-column>
                    <el-table-column prop="amount" label="质押金额">
                        <template slot-scope="scope">
                            {{scope.row.amount}}元
                        </template>
                    </el-table-column>
                    <el-table-column prop="discount_rate" label="折扣率">
                        <template slot-scope="scope">
                            {{(scope.row.discount_rate * 10).toFixed(1)}}折
                        </template>
                    </el-table-column>
                    <el-table-column prop="addtime" label="质押时间" width="160"></el-table-column>
                    <el-table-column prop="endtime" label="到期时间" width="160"></el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row)">
                                {{getStatusText(scope.row)}}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button
                                type="success"
                                size="small"
                                @click="handleExtendZhiya(scope.row)">
                                续期
                            </el-button>
                            <el-button
                                type="danger"
                                size="small"
                                @click="handleCancelZhiya(scope.row)">
                                退押
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <div class="pagination-container" v-if="myZhiya.total > 0">
                    <div class="pagination-info">
                        共 {{myZhiya.total}} 条记录
                    </div>
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-size="pageSize"
                        layout="prev, pager, next"
                        :total="myZhiya.total">
                    </el-pagination>
                </div>
            </el-card>
        </el-main>
    </el-container>
    <!-- 质押确认弹窗 -->
    <el-dialog title="确认质押" :visible.sync="dialogVisible" width="400px">
        <div class="zhiya-confirm">
            <p>分类：{{currentZhiya.category_name}}</p>
            <p>质押金额：{{currentZhiya.amount}}元</p>
            <p>可享折扣：{{(currentZhiya.discount_rate * 10).toFixed(1)}}折</p>
            <p>质押期限：{{currentZhiya.days}}天</p>
            <p>质押无法提前取消，提前退押请联系站长</p>
            <p class="warning">确认质押后将从您的账户扣除相应金额</p>
        </div>
        <div slot="footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmZhiya">确 定</el-button>
        </div>
    </el-dialog>
    <!-- 续期弹窗 -->
    <el-dialog title="续期质押" :visible.sync="extendDialogVisible" width="400px">
        <div class="zhiya-confirm">
            <p>分类：{{currentZhiya.category_name}}</p>
            <p>当前到期时间：{{currentZhiya.endtime}}</p>
            <el-input-number
                v-model="extendDays"
                :min="1"
                :max="365"
                placeholder="请输入续期天数"
                style="width: 100%; margin-top: 10px;">
            </el-input-number>
            <p class="warning">单位（天）续期将延长质押的到期时间</p>
        </div>
        <div slot="footer">
            <el-button @click="extendDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmExtendZhiya">确 定</el-button>
        </div>
    </el-dialog>
</div>
<style>
.zhiya-card {
    margin-bottom: 20px;
    transition: all 0.3s;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}
.zhiya-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    background-color: #eef1f6;
}
.zhiya-card.disabled {
    opacity: 0.7;
}
.zhiya-card .el-card__body {
    padding: 20px;
}
.zhiya-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 15px;
    text-align: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}
.zhiya-amount, .zhiya-discount {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
}
.zhiya-amount .label, .zhiya-discount .label {
    color: #909399;
    margin-right: 8px;
}
.zhiya-amount .value, .zhiya-discount .value {
    font-size: 20px;
    color: #409EFF;
    font-weight: bold;
    text-shadow: 0 0 1px rgba(64, 158, 255, 0.1);
}
.zhiya-amount .unit, .zhiya-discount .unit {
    color: #909399;
    margin-left: 4px;
}
.zhiya-action {
    text-align: center;
    margin-top: 20px;
}
.empty-text {
    text-align: center;
    color: #909399;
    padding: 30px 0;
}
.zhiya-confirm {
    padding: 20px;
}
.zhiya-confirm .warning {
    color: #E6A23C;
    margin-top: 15px;
}
.el-table .expired-row {
    color: #FE716E;
    background: #FFF1F0;
}
</style>
<?php require_once('footer.php'); ?>
<script>
new Vue({
    el: '#app',
    data() {
        return {
            zhiyaList: [],
            myZhiya: { data: [], total: 0 },
            loading: false,
            currentPage: 1,
            pageSize: 10,
            dialogVisible: false,
            extendDialogVisible: false,
            currentZhiya: {},
            extendDays: 1,
            zhiyaMap: new Map()
        }
    },
    methods: {
        // 获取质押方案列表
        getZhiyaList() {
            this.$http.post("/apisub.php?act=zhiyalist", {
                need_page: 0,
                status: 1
            }).then(res => {
                if (res.data.code === 1) {
                    this.zhiyaList = res.data.data;
                    this.zhiyaList.forEach(item => {
                        this.zhiyaMap.set(item.id, item);
                    });
                }
            });
        },
        // 获取我的质押记录
        getMyZhiya() {
            this.loading = true;
            this.$http.post("/apisub.php?act=my_zhiya", {
                page: this.currentPage,
                limit: this.pageSize
            }).then(res => {
                if (res.data.code === 1) {
                    this.myZhiya = {
                        data: (res.data.data || []).map(item => ({
                            ...item,
                            status: parseInt(item.status)
                        })),
                        total: res.data.total || 0,
                        current_page: res.data.current_page || 1,
                        last_page: res.data.last_page || 1
                    };
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        // 获取状态类型
        getStatusType(row) {
            if (row.status === 0) return 'danger';
            return row.status === 2 ? 'warning' : 'success';
        },
        // 获取状态文本
        getStatusText(row) {
            return row.status === 0 ? '已到期' : row.status === 2 ? '待审核' : '生效中';
        },
        // 为表格行添加类名
        tableRowClassName({ row }) {
            if (row.status === 0) return 'expired-row';
            return row.status === 2 ? 'warning-row' : 'success-row';
        },
        // 检查是否已质押某配置
        hasZhiya(config_id) {
            return this.myZhiya.data.some(item => item.config_id === config_id);
        },
        // 处理质押操作
        handleZhiya(item) {
            this.currentZhiya = item;
            this.dialogVisible = true;
        },
        // 确认质押
        confirmZhiya() {
            this.$http.post("/apisub.php?act=user_zhiya", {
                config_id: this.currentZhiya.id
            }).then(res => {
                if (res.data.code === 1) {
                    this.$message.success(res.data.msg);
                    this.dialogVisible = false;
                    this.getMyZhiya();
                } else {
                    this.$message.error(res.data.msg);
                }
            });
        },
        // 处理续期操作
        handleExtendZhiya(row) {
            this.currentZhiya = row;
            this.extendDays = 1;
            this.extendDialogVisible = true;
        },
        // 确认续期
        confirmExtendZhiya() {
            this.$http.post("/apisub.php?act=my_zhiya_addtime", {
                record_id: this.currentZhiya.id,
                date: this.extendDays
            }).then(res => {
                if (res.data.code === 1) {
                    this.$message.success(res.data.msg);
                    this.extendDialogVisible = false;
                    this.getMyZhiya();
                } else {
                    this.$message.error(res.data.msg);
                }
            });
        },
        // 处理退押操作
        handleCancelZhiya(row) {
            this.$confirm('确认取消质押？退还金额将返还至您的账户余额。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.post("/apisub.php?act=my_zhiya_del", {
                    record_id: row.id
                }).then(res => {
                    if (res.data.code === 1) {
                        this.$message.success(res.data.msg);
                        this.getMyZhiya();
                    } else {
                        this.$message.error(res.data.msg);
                    }
                });
            }).catch(() => {
                this.$message.info('已取消操作');
            });
        },
        // 处理分页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getMyZhiya();
        }
    },
    mounted() {
        this.getZhiyaList();
        this.getMyZhiya();
    }
});
</script>