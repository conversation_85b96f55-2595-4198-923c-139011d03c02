<?php
$mod='blank';
$title='课程ID对比';
require_once('head.php');
?>

<div class="app-content-body">
    <div class="wrapper-md control">		
        <div class="row">
            <div class="col-sm-12">
                <!-- 保留这个标题行 -->
                <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                    课程ID对比
                </div>

                <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">    
                    <div class="table-responsive"> 
                        <!-- Element UI 表格容器 -->
                        <div id="app">
                            <template>
                                <el-table :data="tableData" border style="width: 100%" v-loading="loading">
                                    <el-table-column prop="oid" label="订单ID" width="75"></el-table-column>
                                    <el-table-column prop="ptname" label="商品名称" width="240"></el-table-column>
                                    <el-table-column prop="user" label="下单账号" width="150"></el-table-column>
                                    <el-table-column prop="kcname" label="课程名称" width="300"></el-table-column>
                                    <el-table-column prop="kcid" label="课程ID" width="600"></el-table-column>
                                    <el-table-column prop="addtime" label="提交时间" width="250"></el-table-column>
                                    <el-table-column prop="status" label="订单状态" width="250"></el-table-column>
                                </el-table>

                                <!-- 分页组件 -->
                                <el-pagination
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    :current-page="currentPage"
                                    :page-sizes="[20, 50, 100]"
                                    :page-size="pageSize"
                                    layout="total, sizes, prev, pager, next"
                                    :total="total">
                                </el-pagination>
                            </template>
                        </div>
                    </div>
                </div>		
            </div>
        </div>
    </div>
</div>

<?php require_once("lightyearfooter.php");?> 
<?php require_once("footer.php");?>


<script>
new Vue({
    el: '#app',
    data() {
        return {
            tableData: [],
            loading: true,
            currentPage: 1,
            pageSize: 20,
            total: 0
        };
    },
    mounted() {
        this.fetchData();
    },
    methods: {
        fetchData() {
            this.loading = true;
            axios.get('../apisub.php?act=kcidlist', {
                params: {
                    page: this.currentPage,
                    limit: this.pageSize
                }
            }).then(response => {
                const res = response.data;
                if (res.code === 1) {
                    this.tableData = res.data;
                    this.total = res.count;
                }
                this.loading = false;
            }).catch(error => {
                console.error("请求失败", error);
                this.loading = false;
            });
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.fetchData();
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            this.fetchData();
        }
    }
});
</script>