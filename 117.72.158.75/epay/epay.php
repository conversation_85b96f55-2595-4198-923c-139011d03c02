<!DOCTYPE html>
<html lang="zh-cn">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<title>正在跳转支付界面...</title>
</head>
<?php
include("../confing/common.php");
require_once("epay/submit.class.php");
$type = $_POST['type'];
$allowed_types = ['wxpay', 'usdt', 'alipay'];
if (!in_array($type, $allowed_types)) {
    die('支付方式不合法');
} 
$out_trade_no = $_POST['out_trade_no']; //支付单号 
$out_trade_no = preg_replace('/[^0-9]/', '', $out_trade_no);
$row = $DB->prepare_getrow("SELECT * FROM qingka_wangke_pay WHERE `out_trade_no`=? LIMIT 1", [$out_trade_no]);
$DB->prepare_query("UPDATE `qingka_wangke_pay` SET `type`=? WHERE `out_trade_no`=?", [$type, $out_trade_no]);
$protocol = isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https' ? 'https://' : 'http://';
$domain = $_SERVER['HTTP_HOST'];
$notify_url = $protocol . $domain . '/epay/notify_url.php';
$return_url = $protocol . $domain . '/epay/return_url.php';
$parameter = array(
    "pid" => trim($alipay_config['partner']),
    "type" => $type,
    "notify_url" => $notify_url,
    "return_url" => $return_url,
    "out_trade_no" => $row['out_trade_no'],
    "name" => $row['name'],
    "money" => $row['money'],
    "sitename" => $domain
);
//建立请求
$alipaySubmit = new AlipaySubmit($alipay_config);
$html_text = $alipaySubmit->buildRequestForm($parameter);
echo $html_text;
?>
</body>
</html>