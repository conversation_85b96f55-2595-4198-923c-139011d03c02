<?php 
function getMillisecond() { 

    list($t1, $t2) = explode(' ', microtime()); 
  
    return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000); 
  
}
// 查课接口设置
function getWk($type, $noun, $school, $user, $pass, $name = false){
	global $DB;
	global $wk;
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$type}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	
    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=get";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }

    // yyy教育查课接口
    if ($type == "yyy") {
        // 先登录获取token
        $login_data = array(
            "username" => $a["user"],
            "password" => $a["pass"]
        );
        $login_url = $a["url"] . "/api/login";
        $headers = array(
            "Content-Type: application/json",
            "Accept: application/json, text/plain, */*",
            "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "X-Requested-With: XMLHttpRequest"
        );
        $login_result = get_url3($login_url, json_encode($login_data), "", $headers);
        $login_response = json_decode($login_result, true);

        if ($login_response['code'] != 200) {
            return array("code" => -1, "msg" => "登录失败: " . $login_response['message']);
        }

        $access_token = $login_response['data']['accessToken'];
        $refresh_token = $login_response['data']['refreshToken'];

        // 构建Cookie
        $token_data = array(
            "accessToken" => $access_token,
            "expires" => 1919520000000,
            "refreshToken" => $refresh_token
        );
        $token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
        $encoded_token = urlencode($token_json);
        $cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

        // 查询课程 - 使用制表符分隔账号密码
        $order_data = array(
            "lastoid" => $noun, // 使用传入的noun作为lastoid
            "orderData" => $user . "\t" . $pass, // 制表符分隔
            "orderNote" => "",
            "search" => "1"
        );

        $query_headers = array(
            "Content-Type: application/json",
            "Accept: application/json, text/plain, */*",
            "Authorization: Bearer " . $access_token,
            "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "X-Requested-With: XMLHttpRequest"
        );

        $query_url = $a["url"] . "/api/order";
        $query_result = get_url3($query_url, json_encode($order_data), $cookie, $query_headers);
        $query_response = json_decode($query_result, true);

        if ($query_response['code'] != 200) {
            return array("code" => -1, "msg" => "查课失败: " . ($query_response['message'] ?? "未知错误"));
        }

        // 解析课程数据 (格式: "账号----密码----课程名称")
        $courses = array();
        if (isset($query_response['data']) && is_array($query_response['data'])) {
            foreach ($query_response['data'] as $course_info) {
                if (strpos($course_info, "----") !== false) {
                    $parts = explode("----", $course_info);
                    if (count($parts) >= 3) {
                        $courses[] = array(
                            'name' => $parts[2], // 课程名称 - 系统期望的字段
                            'account' => $parts[0],
                            'password' => $parts[1],
                            'course_name' => $parts[2],
                            'full_info' => $course_info,
                            'user' => $parts[0], // 兼容字段
                            'pass' => $parts[1], // 兼容字段
                            'kcname' => $parts[2] // 兼容字段
                        );
                    }
                } else {
                    // 如果不是标准格式，直接作为课程名称
                    $courses[] = array(
                        'name' => $course_info,
                        'course_name' => $course_info,
                        'kcname' => $course_info,
                        'full_info' => $course_info
                    );
                }
            }
        }

        if (empty($courses)) {
            return array("code" => -1, "msg" => "未找到课程信息或账号密码错误");
        }

        return array(
            "code" => 1,
            "msg" => "查询成功",
            "data" => $courses
        );
    }

       //暗网查课
    if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=get";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       return $result;
    }
    
	else {
    print_r("没有了,文件ckjk.php,可能故障：参数缺少，比如平台名错误！！！");die;
	}
}
 

?>