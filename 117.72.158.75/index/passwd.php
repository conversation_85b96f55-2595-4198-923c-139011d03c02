<?php
$mod='blank';
$title='修改密码';
require_once('head.php');
?> 
        <div class="wrapper-md control">
	       <div class="panel panel-default" id="add">
		      		    <div class="panel-heading font-bold layui-bg-gray"  style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">修改密码</div>
				 <div class="panel-body"  style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
			          <form action="" method="post" class="form-horizontal" role="form">
			            <div class="input-group">
			              <span class="input-group-addon"><span class="glyphicon glyphicon-lock"></span></span>
			              <input type="text" v-model="oldpass" value="" class="form-control" placeholder="旧密码" required="required"/>
			            </div><br/>
			            <div class="input-group">
			              <span class="input-group-addon"><span class="glyphicon glyphicon-lock"></span></span>
			              <input type="text" v-model="newpass" class="form-control" placeholder="新密码" required="required"/>
			            </div><br/>
			            <div class="form-group">
			              <div class="col-xs-12"><input type="button" @click="passwd" name="submit" value="修改" class="btn btn-dark form-control"/></div>
			            </div>
			          </form>
			        </div>
          </div>
       </div>
  </div>
<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script>
	new Vue({
		el:"#add",
		data:{
			oldpass:'',
			newpass:''
		},
		methods:{
			passwd:function(){
			  var load=layer.load(2);
              $.post("/apisub.php?act=passwd",{oldpass:this.oldpass,newpass:this.newpass},function (data) {
		 	     layer.close(load);
	             if (data.code==1){	   
	                layer.alert(data.msg,{icon:1});
	             }else{
	                layer.msg(data.msg,{icon:2});
	             }
              });
			}
		}
	});
	
</script>