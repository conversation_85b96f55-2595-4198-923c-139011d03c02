<?php
$title = '批量交单';
require_once('head.php');
$addsalt = md5(mt_rand(0, 999) . time());
$_SESSION['addsalt'] = $addsalt;
?>

<div class="wrapper-md control" id="add" v-cloak>
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default" id="addorder" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">无查提交</div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
                    <form class="form-horizontal devform">
                        <div class="form-group">
                            <label class="col-sm-1 control-label">选择项目:</label>
                            <div class="col-sm-9">
<el-select id="select" v-model="cid" @change="tips(cid)" filterable placeholder="直接输入名称即可搜索" style="width:100%">
    <el-option 
        v-for="class2 in class1" 
        :key="class2.cid" 
        :label="class2.name" 
        :value="class2.cid"
    >
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
            <span style="max-width: 230px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ class2.name }}
            </span>
            <span style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ class2.price }}积分
            </span>
        </div>
    </el-option>
</el-select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-1 control-label">账号信息:</label>
                            <div class="col-sm-9">
                                <textarea rows="12" cols="80" class="layui-textarea" v-model="userinfo" placeholder="填写格式：&#10例： 手机号 密码 课程 &#10或： 学校 账号 密码 课程 &#10多账号下单必须换行！ &#10务必一行一条信息且账号密码正确！" style="border-radius: 8px;"></textarea>
                                <div style="height:5px"></div>
                                <span class="help-block m-b-none" style="color:red;" id="warning">
                                    <span v-html="content"></span>
                                </span>
                            </div>
                        </div>
                        <div class="col-sm-offset-1">
                            <button type="button" @click="showExcelModal" class="layui-btn layui-btn-warm">上传Excel</button>
                            <input type="button" @click="checkUserinfo" value="检查数据" class="layui-btn layui-btn-danger" />
                            <input type="button" @click="add" value="立即提交" class="layui-btn layui-btn-normal" />
                            <input type="reset" value="重置" class="layui-btn layui-btn-primary" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="wrapper-md control" id="loglist" style="margin-top:-35px">
            <div class="panel panel-default" id="addorder" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">提交结果</div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>平台 学校 账号 密码 课程名称</th>
                            <th>预计扣费</th>
                            <th>余额</th>
                            <th>操作时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="res in row.data">
                            <td>{{res.id}}</td>
                            <td>{{res.text}}</td>
                            <td>{{res.money}}</td>
                            <td>{{res.smoney}}</td>
                            <td>{{res.addtime}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <ul class="pagination" v-if="row.last_page > 1">
                <!--by 青卡 Vue分页 -->
                <li class="disabled"><a @click="get(1)">首页</a></li>
                <li class="disabled"><a @click="row.current_page > 1 ? get(row.current_page - 1) : ''">&laquo;</a></li>
                <li @click="get(row.current_page - 3)" v-if="row.current_page - 3 >= 1"><a>{{ row.current_page - 3 }}</a></li>
                <li @click="get(row.current_page - 2)" v-if="row.current_page - 2 >= 1"><a>{{ row.current_page - 2 }}</a></li>
                <li @click="get(row.current_page - 1)" v-if="row.current_page - 1 >= 1"><a>{{ row.current_page - 1 }}</a></li>
                <li :class="{'active': row.current_page == row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
                <li @click="get(row.current_page + 1)" v-if="row.current_page + 1 <= row.last_page"><a>{{ row.current_page + 1 }}</a></li>
                <li @click="get(row.current_page + 2)" v-if="row.current_page + 2 <= row.last_page"><a>{{ row.current_page + 2 }}</a></li>
                <li @click="get(row.current_page + 3)" v-if="row.current_page + 3 <= row.last_page"><a>{{ row.current_page + 3 }}</a></li>
                <li class="disabled"><a @click="row.last_page > row.current_page ? get(row.current_page + 1) : ''">&raquo;</a></li>
                <li class="disabled"><a @click="get(row.last_page)">尾页</a></li>
            </ul>
        </div>
    </div>
</div>

<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>

<script>
var vm = new Vue({
    el: "#add",
    data: {
        row: [],
        check_row: [],
        userinfo: '',
        cid: '',
        class1: '',
        class3: '',
        activems: false,
        content: '',
        excelData: []
    },
    methods: {
        add: function () {
            if (!this.cid) {
                layer.msg("请先选择平台");
                return false;
            }
            if (!this.userinfo) {
                layer.msg("请填写信息");
                return false;
            }
            var userinfo = this.userinfo.replace(/\r\n/g, "[br]").replace(/\n/g, "[br]").replace(/\r/g, "[br]");
            userinfo = userinfo.split('[br]'); // 分割

            for (var i = 0; this.class1.length > i; i++) {
                if (this.class1[i].cid == this.cid) {
                    var price = this.class1[i].price;
                    break;
                }
            }

            var kofei = price * userinfo.length;
            var num = userinfo.length;

            layer.confirm("检测到有<b style='color:red'>" + num + "</b>条账号信息，预计扣费<b style='color:red'>" + kofei + "积分</b>，具体扣费以提交结果为准", {
                title: '温馨提示',
                icon: 3,
                btn: ['确定交单', '取消']
            }, function () {
                var loading = layer.load(2);
                vm.$http.post("/apisub.php?act=add_pl", {
                    cid: vm.cid,
                    userinfo: userinfo,
                    num: num
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(loading);
                    if (data.data.code == 1) {
                        layer.alert(data.data.msg, {
                            icon: 1,
                            title: "提交结果"
                        }, function () {
                            window.location.href = "";
                        });
                    } else {
                        layer.alert(data.data.msg, {
                            icon: 2,
                            title: "提交结果"
                        });
                    }
                }, function () {
                    layer.close(loading);
                    layer.alert("服务器错误");
                });
            });
        },
        getclass: function () {
            var load = layer.load(2);
            this.$http.post("/apisub.php?act=getclassfl").then(function (data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.class1 = data.body.data;
                } else {
                    layer.msg(data.data.msg, {
                        icon: 2
                    });
                }
            });
        },
        showExcelModal: function () {
            layer.open({
                type: 1,
                title: '上传Excel',
                content: 
                    `<input type="file" id="excelFile" accept=".xlsx,.xls" />` +
                    `<button type="button" id="readExcelBtn" onclick="vm.readExcel()">读取并解析文件</button>`+
                    `<br>文件上传后点读取并解析文件<br>所有账号信息会显示在上方的框中<br>读取时会默认丢弃第一行的数据（表头）<br>默认表格格式为 学校 账号 密码 课程`,
                area: ['300px', '200px']
            });
        },
        readExcel: function () {
            var file = document.getElementById('excelFile').files[0];
            if (!file) {
                layer.msg("请选择Excel文件");
                return;
            }
            var reader = new FileReader();
            reader.onload = function (e) {
                var data = e.target.result;
                var workbook = XLSX.read(data, { type: 'binary' });
                var sheetName = workbook.SheetNames[0];
                var sheet = workbook.Sheets[sheetName];
                var json = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '' });
                var userData = json.slice(1).map(row => row.join(' '));
                vm.userinfo = userData.join('\n');
                layer.msg("数据读取成功");
                layer.closeAll();
            };
            reader.readAsBinaryString(file);
        },
        checkUserinfo: function () {
        var lines = this.userinfo.split('\n');
        var errors = [];
        var correctedLines = [];
        for (var i = 0; i < lines.length; i++) {
            var line = lines[i].trim(); // 去除行首和行尾的空格
            var correctedLine = line.replace(/ +/g, ' '); // 替换多个空格为单个空格
            correctedLines.push(correctedLine);
            var parts = correctedLine.split(' ');
            if (parts.length < 3 || parts.length > 4) {
                errors.push('第' + (i+1) + '行数据数量不正确，此行有' + parts.length + '个数据。');
            }
        }
        if (errors.length > 0) {
            layer.alert(errors.join('\n'), {title: '数据检查结果', icon: 5});
        } else {
            layer.msg('数据检查通过！', {icon: 1});
        }
        // 修正 userinfo 中的多于空格
        this.userinfo = correctedLines.join('\n');
    },
        tips: function (message) {
            for (var i = 0; this.class1.length > i; i++) {
                if (this.class1[i].cid == message) {
                    this.show = true;
                    this.content = this.class1[i].content;
                    return false;
                }
            }
        },
    },
    mounted() {
        this.getclass();
    }
});
</script>
<script>
new Vue({
    el: "#loglist",
    data: {
        row: null,
        type: '',
        types: '',
        qq: ''
    },
    methods: {
        get: function (page, a) {
            var load = layer.load(2);
            data = {
                page: page,
                type: this.type,
                types: this.types,
                qq: this.qq
            }
            this.$http.post("/apisub.php?act=loglist1", data, {
                emulateJSON: true
            }).then(function (data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.row = data.body;
                } else {
                    layer.msg(data.data.msg, {
                        icon: 2
                    });
                }
            });
        }
    },
    mounted() {
        this.get(1);
    }
});
</script>