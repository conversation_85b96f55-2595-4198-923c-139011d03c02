<?php
/**
 * yyy教育项目直接同步脚本
 * 绕过混淆的配置文件，直接使用数据库配置
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>yyy教育项目同步</title></head><body>";
echo "<h1>yyy教育项目直接同步</h1>";

// 直接包含数据库配置
try {
    include 'confing/mysqlset.php';
    echo "<p style='color:green'>✅ 数据库配置加载成功</p>";

    // 显示配置信息（隐藏密码）
    echo "<p>数据库主机: {$host}</p>";
    echo "<p>数据库端口: {$port}</p>";
    echo "<p>数据库用户: {$user}</p>";
    echo "<p>数据库名称: {$dbname}</p>";
    echo "<p>密码: " . str_repeat('*', strlen($pwd)) . "</p>";

} catch (Exception $e) {
    echo "<p style='color:red'>❌ 数据库配置加载失败: " . $e->getMessage() . "</p>";
    exit;
}

// 创建数据库连接
try {
    $mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
    if ($mysqli->connect_error) {
        throw new Exception("连接失败: " . $mysqli->connect_error);
    }
    $mysqli->set_charset("utf8");
    echo "<p style='color:green'>✅ 数据库连接成功</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    echo "<p>尝试连接: {$user}@{$host}:{$port}/{$dbname}</p>";
    exit;
}

// HTTP请求函数
function simple_curl($url, $data = null, $headers = array()) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $result;
}

try {
    // 获取yyy教育货源配置
    echo "<p>🔍 正在获取yyy教育货源配置...</p>";
    $stmt = $mysqli->prepare("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'yyy' LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    $huoyuan = $result->fetch_assoc();
    $stmt->close();
    
    if (!$huoyuan) {
        echo "<p style='color:red'>❌ 未找到yyy教育货源配置</p>";
        exit;
    }
    echo "<p style='color:green'>✅ 找到货源配置 (ID: {$huoyuan['hid']})</p>";

    // 获取yyy教育分类ID
    echo "<p>🔍 正在获取yyy教育分类...</p>";
    $stmt = $mysqli->prepare("SELECT id FROM qingka_wangke_fenlei WHERE name = 'yyy教育' LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    $fenlei = $result->fetch_assoc();
    $stmt->close();
    
    if (!$fenlei) {
        echo "<p style='color:red'>❌ 未找到yyy教育分类</p>";
        exit;
    }
    $fenlei_id = $fenlei['id'];
    echo "<p style='color:green'>✅ 找到分类 (ID: {$fenlei_id})</p>";

    // 第一步：登录获取token
    echo "<p>🔐 正在登录yyy教育API...</p>";
    $login_data = json_encode(array(
        "username" => $huoyuan["user"], 
        "password" => $huoyuan["pass"]
    ));
    $login_url = $huoyuan["url"] . "/api/login";
    $headers = array(
        "Content-Type: application/json",
        "Accept: application/json, text/plain, */*",
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    );
    
    $login_result = simple_curl($login_url, $login_data, $headers);
    
    if (!$login_result) {
        echo "<p style='color:red'>❌ API请求失败，无响应</p>";
        exit;
    }
    
    echo "<p style='color:green'>✅ API请求成功</p>";
    echo "<p>响应内容: <pre>" . htmlspecialchars(substr($login_result, 0, 500)) . "...</pre></p>";
    
    $login_response = json_decode($login_result, true);
    if (!$login_response || $login_response['code'] != 200) {
        echo "<p style='color:red'>❌ API登录失败: " . ($login_response['message'] ?? '未知错误') . "</p>";
        exit;
    }
    
    $access_token = $login_response['data']['accessToken'];
    $refresh_token = $login_response['data']['refreshToken'];
    echo "<p style='color:green'>✅ 登录成功，用户: {$login_response['data']['username']}</p>";
    
    // 构建Cookie
    $token_data = array(
        "accessToken" => $access_token,
        "expires" => 1919520000000,
        "refreshToken" => $refresh_token
    );
    $token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
    $encoded_token = urlencode($token_json);
    $cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";
    
    // 第二步：获取网站列表
    echo "<p>📋 正在获取项目列表...</p>";
    $site_data = json_encode(array("version" => ""));
    $site_headers = array(
        "Content-Type: application/json",
        "Accept: application/json, text/plain, */*",
        "Authorization: Bearer " . $access_token,
        "Cookie: " . $cookie,
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    );
    
    $site_url = $huoyuan["url"] . "/api/site";
    $site_result = simple_curl($site_url, $site_data, $site_headers);
    
    if (!$site_result) {
        echo "<p style='color:red'>❌ 获取网站列表失败：无响应</p>";
        exit;
    }
    
    $site_response = json_decode($site_result, true);
    if (!$site_response || $site_response['code'] != 200) {
        echo "<p style='color:red'>❌ 获取网站列表失败: " . ($site_response['message'] ?? '未知错误') . "</p>";
        echo "<p>响应内容: <pre>" . htmlspecialchars(substr($site_result, 0, 500)) . "...</pre></p>";
        exit;
    }
    
    $projects = $site_response['data']['list'];
    $total_count = count($projects);
    echo "<p style='color:green'>✅ 获取到 {$total_count} 个项目</p>";
    echo "<p>📊 版本: {$site_response['data']['version']}, 最新订单ID: {$site_response['data']['lastoid']}</p>";
    
    // 第三步：同步项目到数据库
    echo "<p>💾 正在同步项目到数据库...</p>";
    echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
    
    $success_count = 0;
    $error_count = 0;
    $update_count = 0;
    
    foreach ($projects as $index => $project) {
        try {
            // 解析价格
            $price_str = $project['price_unit'];
            $price = 0;
            if (preg_match('/(\d+\.?\d*)/', $price_str, $matches)) {
                $price = floatval($matches[1]);
            }
            
            // 检查是否已存在
            $check_stmt = $mysqli->prepare("SELECT cid FROM qingka_wangke_class WHERE noun = ? AND fenlei = ? LIMIT 1");
            $check_stmt->bind_param("si", $project['id'], $fenlei_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            $existing = $check_result->fetch_assoc();
            $check_stmt->close();
            
            if ($existing) {
                // 更新现有商品
                $update_stmt = $mysqli->prepare("UPDATE qingka_wangke_class SET name = ?, price = ?, content = ?, uptime = ? WHERE cid = ?");
                $name = 'yyy教育-' . $project['name'];
                $content = $project['trans'] . ' (yyy教育聚合平台)';
                $uptime = date('Y-m-d H:i:s');
                $update_stmt->bind_param("ssssi", $name, $price, $content, $uptime, $existing['cid']);
                $update_stmt->execute();
                $update_stmt->close();
                $update_count++;
                echo "<small style='color:blue'>🔄 更新: {$project['name']} (ID: {$project['id']})</small><br>";
            } else {
                // 插入新商品 - 包含所有必要字段
                $insert_stmt = $mysqli->prepare("INSERT INTO qingka_wangke_class (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, status, fenlei, addtime, uptime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $sort = 50;
                $name = 'yyy教育-' . $project['name'];
                $getnoun = $project['id'];
                $noun = $project['id'];
                $price_str = (string)$price; // 转换为字符串，因为数据库字段是varchar
                $queryplat = (string)$huoyuan['hid'];
                $docking = (string)$huoyuan['hid'];
                $yunsuan = '*';
                $content = $project['trans'] . ' (yyy教育聚合平台)';
                $status = 1;
                $fenlei_str = (string)$fenlei_id;
                $addtime = date('Y-m-d H:i:s');
                $uptime = date('Y-m-d H:i:s');

                $insert_stmt->bind_param("issssssssisss", $sort, $name, $getnoun, $noun, $price_str, $queryplat, $docking, $yunsuan, $content, $status, $fenlei_str, $addtime, $uptime);

                if ($insert_stmt->execute()) {
                    $success_count++;
                    echo "<small style='color:green'>➕ 新增: {$project['name']} (ID: {$project['id']}, 价格: {$price})</small><br>";
                } else {
                    $error_count++;
                    echo "<small style='color:red'>❌ 插入失败: {$project['name']} - {$insert_stmt->error}</small><br>";
                }
                $insert_stmt->close();
            }
            
            // 每100个项目显示一次进度
            if (($index + 1) % 100 == 0) {
                echo "<p><strong>进度: " . ($index + 1) . "/{$total_count}</strong></p>";
                flush();
            }
            
        } catch (Exception $e) {
            $error_count++;
            echo "<small style='color:red'>❌ 错误: {$project['name']} - {$e->getMessage()}</small><br>";
        }
    }
    
    echo "</div>";
    
    // 显示同步结果
    echo "<hr>";
    echo "<h2>🎉 同步完成！</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<p><strong>同步统计:</strong></p>";
    echo "<ul>";
    echo "<li>总项目数: <strong>{$total_count}</strong></li>";
    echo "<li>新增项目: <strong style='color:green'>{$success_count}</strong></li>";
    echo "<li>更新项目: <strong style='color:blue'>{$update_count}</strong></li>";
    echo "<li>错误数量: <strong style='color:red'>{$error_count}</strong></li>";
    echo "<li>API版本: <strong>{$site_response['data']['version']}</strong></li>";
    echo "<li>最新订单ID: <strong>{$site_response['data']['lastoid']}</strong></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p style='color:green'><strong>✅ 现在您可以在系统中看到所有yyy教育的项目了！</strong></p>";
    echo "<p><a href='index/home.php' style='color:blue'>👉 点击这里返回系统首页</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 同步失败: " . $e->getMessage() . "</p>";
    echo "<p>错误详情: " . $e->getFile() . " 第 " . $e->getLine() . " 行</p>";
}

// 关闭数据库连接
if (isset($mysqli)) {
    $mysqli->close();
}

echo "</body></html>";
?>
