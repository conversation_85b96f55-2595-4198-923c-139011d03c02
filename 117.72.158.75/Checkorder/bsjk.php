<?php  
function budanWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,yid,user from qingka_wangke_order where oid='{$oid}' ");
	$hid = $b["hid"];
	$yid = $b["yid"];
	$user = $b["user"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];

	// yyy教育补刷接口 - 重新提交订单
	if ($type == "yyy") {
		// yyy教育没有专门的补刷接口，通过重新下单来实现补刷
		// 先登录获取token
		$login_data = array(
			"username" => $a["user"],
			"password" => $a["pass"]
		);
		$login_url = $a["url"] . "/api/login";
		$headers = array(
			"Content-Type: application/json",
			"Accept: application/json, text/plain, */*",
			"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"X-Requested-With: XMLHttpRequest"
		);
		$login_result = get_url3($login_url, json_encode($login_data), "", $headers);
		$login_response = json_decode($login_result, true);

		if ($login_response['code'] != 200) {
			return array("code" => -1, "msg" => "登录失败: " . $login_response['message']);
		}

		$access_token = $login_response['data']['accessToken'];
		$refresh_token = $login_response['data']['refreshToken'];

		// 构建Cookie
		$token_data = array(
			"accessToken" => $access_token,
			"expires" => 1919520000000,
			"refreshToken" => $refresh_token
		);
		$token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
		$encoded_token = urlencode($token_json);
		$cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

		// 重新提交订单实现补刷
		$order_data = array(
			"lastoid" => $d["noun"],
			"orderData" => $d["user"] . " " . $d["pass"] . " " . $d["kcname"],
			"orderNote" => "补刷订单",
			"search" => "0"
		);

		$order_headers = array(
			"Content-Type: application/json",
			"Accept: application/json, text/plain, */*",
			"Authorization: Bearer " . $access_token,
			"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"X-Requested-With: XMLHttpRequest"
		);

		$order_url = $a["url"] . "/api/order";
		$order_result = get_url3($order_url, json_encode($order_data), $cookie, $order_headers);
		$order_response = json_decode($order_result, true);

		if ($order_response && isset($order_response['code'])) {
			if ($order_response['code'] == 200) {
				return array("code" => 1, "msg" => "补刷成功");
			} else {
				return array("code" => -1, "msg" => $order_response['message'] ?? "补刷失败");
			}
		} else {
			return array("code" => -1, "msg" => "补刷接口返回数据异常");
		}
	}

    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=budan";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }
    
    //暗网补刷
    if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=budan";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       return $result;
    }
	else {
				$b = array("code" => -1, "msg" => "接口异常，请联系管理员");
		}
}




?>