<?php
/**
 * 检查数据库表结构脚本
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>数据库表结构检查</title></head><body>";
echo "<h1>数据库表结构检查</h1>";

// 包含数据库配置
include 'confing/mysqlset.php';

// 创建数据库连接
try {
    $mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
    if ($mysqli->connect_error) {
        throw new Exception("连接失败: " . $mysqli->connect_error);
    }
    $mysqli->set_charset("utf8");
    echo "<p style='color:green'>✅ 数据库连接成功</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    exit;
}

// 检查表结构
$tables_to_check = [
    'qingka_wangke_huoyuan',
    'qingka_wangke_fenlei', 
    'qingka_wangke_class'
];

foreach ($tables_to_check as $table) {
    echo "<h2>📋 表: {$table}</h2>";
    
    // 检查表是否存在
    $result = $mysqli->query("SHOW TABLES LIKE '{$table}'");
    if ($result->num_rows == 0) {
        echo "<p style='color:red'>❌ 表 {$table} 不存在</p>";
        continue;
    }
    
    echo "<p style='color:green'>✅ 表 {$table} 存在</p>";
    
    // 显示表结构
    echo "<h3>表结构:</h3>";
    $result = $mysqli->query("DESCRIBE {$table}");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 显示数据统计
    $count_result = $mysqli->query("SELECT COUNT(*) as count FROM {$table}");
    $count = $count_result->fetch_assoc()['count'];
    echo "<p><strong>数据行数: {$count}</strong></p>";
    
    // 如果是商品表，显示yyy教育相关数据
    if ($table == 'qingka_wangke_class') {
        echo "<h3>yyy教育相关商品:</h3>";
        $yyy_result = $mysqli->query("SELECT cid, name, noun, price, fenlei, status FROM {$table} WHERE name LIKE '%yyy教育%' LIMIT 10");
        if ($yyy_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>名称</th><th>标识</th><th>价格</th><th>分类</th><th>状态</th></tr>";
            while ($row = $yyy_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['cid']}</td>";
                echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                echo "<td>{$row['noun']}</td>";
                echo "<td>{$row['price']}</td>";
                echo "<td>{$row['fenlei']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color:red'>❌ 没有找到yyy教育相关商品</p>";
        }
    }
    
    // 如果是分类表，显示yyy教育分类
    if ($table == 'qingka_wangke_fenlei') {
        echo "<h3>yyy教育分类:</h3>";
        $yyy_result = $mysqli->query("SELECT * FROM {$table} WHERE name LIKE '%yyy教育%'");
        if ($yyy_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>名称</th><th>描述</th><th>排序</th><th>状态</th></tr>";
            while ($row = $yyy_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['text'] ?? '') . "</td>";
                echo "<td>{$row['sort']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color:red'>❌ 没有找到yyy教育分类</p>";
        }
    }
    
    // 如果是货源表，显示yyy教育货源
    if ($table == 'qingka_wangke_huoyuan') {
        echo "<h3>yyy教育货源:</h3>";
        $yyy_result = $mysqli->query("SELECT * FROM {$table} WHERE pt = 'yyy'");
        if ($yyy_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>平台</th><th>名称</th><th>URL</th><th>用户</th><th>状态</th></tr>";
            while ($row = $yyy_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['hid']}</td>";
                echo "<td>{$row['pt']}</td>";
                echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                echo "<td>{$row['url']}</td>";
                echo "<td>{$row['user']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color:red'>❌ 没有找到yyy教育货源</p>";
        }
    }
    
    echo "<hr>";
}

// 检查最近的插入操作
echo "<h2>🔍 最近的数据库操作检查</h2>";

// 检查是否有最近插入的数据
$recent_result = $mysqli->query("SELECT cid, name, addtime FROM qingka_wangke_class WHERE name LIKE '%yyy教育%' ORDER BY addtime DESC LIMIT 5");
if ($recent_result && $recent_result->num_rows > 0) {
    echo "<h3>最近插入的yyy教育商品:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>名称</th><th>添加时间</th></tr>";
    while ($row = $recent_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['cid']}</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>{$row['addtime']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>❌ 没有找到最近插入的yyy教育商品</p>";
}

// 检查数据库错误日志
echo "<h2>🔍 可能的问题分析</h2>";

// 检查字段是否存在
$fields_to_check = ['addtime', 'uptime', 'sort', 'getnoun', 'noun', 'queryplat', 'docking', 'yunsuan', 'content', 'status', 'fenlei'];
$missing_fields = [];

$desc_result = $mysqli->query("DESCRIBE qingka_wangke_class");
$existing_fields = [];
while ($row = $desc_result->fetch_assoc()) {
    $existing_fields[] = $row['Field'];
}

foreach ($fields_to_check as $field) {
    if (!in_array($field, $existing_fields)) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    echo "<p style='color:red'>❌ 缺少字段: " . implode(', ', $missing_fields) . "</p>";
} else {
    echo "<p style='color:green'>✅ 所有必要字段都存在</p>";
}

// 测试插入一条数据
echo "<h2>🧪 测试插入数据</h2>";
try {
    $test_stmt = $mysqli->prepare("INSERT INTO qingka_wangke_class (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, status, fenlei, addtime, uptime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $sort = 999;
    $name = '测试-yyy教育项目';
    $getnoun = 'test123';
    $noun = 'test123';
    $price = '1.00';
    $queryplat = '43'; // 使用之前找到的货源ID
    $docking = '43';
    $yunsuan = '*';
    $content = '这是一个测试项目';
    $status = 1;
    $fenlei_id = '100'; // 使用之前找到的分类ID
    $addtime = date('Y-m-d H:i:s');
    $uptime = date('Y-m-d H:i:s');

    $test_stmt->bind_param("issssssssisss", $sort, $name, $getnoun, $noun, $price, $queryplat, $docking, $yunsuan, $content, $status, $fenlei_id, $addtime, $uptime);

    if ($test_stmt->execute()) {
        $insert_id = $mysqli->insert_id;
        echo "<p style='color:green'>✅ 测试插入成功，ID: {$insert_id}</p>";

        // 删除测试数据
        $mysqli->query("DELETE FROM qingka_wangke_class WHERE cid = {$insert_id}");
        echo "<p style='color:blue'>🗑️ 测试数据已清理</p>";
    } else {
        echo "<p style='color:red'>❌ 测试插入失败: " . $test_stmt->error . "</p>";
    }
    $test_stmt->close();
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 测试插入异常: " . $e->getMessage() . "</p>";
}

$mysqli->close();
echo "</body></html>";
?>
