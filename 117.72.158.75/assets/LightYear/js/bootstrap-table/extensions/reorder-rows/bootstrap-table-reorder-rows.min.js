/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableReorderRows={exports:{}}.exports}})(this,function(){'use strict';(function(a){var b=function(a,b){return{id:'customId_'+b}};a.extend(a.fn.bootstrapTable.defaults,{reorderableRows:!1,onDragStyle:null,onDropStyle:null,onDragClass:'reorder_rows_onDragClass',dragHandle:null,useRowAttrFunc:!1,onReorderRowsDrag:function(){return!1},onReorderRowsDrop:function(){return!1},onReorderRow:function(){return!1}}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"reorder-row.bs.table":'onReorderRow'});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.init,e=c.prototype.initSearch;c.prototype.init=function(){if(!this.options.reorderableRows)return void d.apply(this,Array.prototype.slice.apply(arguments));var a=this;this.options.useRowAttrFunc&&(this.options.rowAttributes=b);var c=this.options.onPostBody;this.options.onPostBody=function(){setTimeout(function(){a.makeRowsReorderable(),c.apply()},1)},d.apply(this,Array.prototype.slice.apply(arguments))},c.prototype.initSearch=function(){e.apply(this,Array.prototype.slice.apply(arguments));!this.options.reorderableRows},c.prototype.makeRowsReorderable=function(){if(!this.options.cardView){var a=this;this.$el.tableDnD({onDragStyle:a.options.onDragStyle,onDropStyle:a.options.onDropStyle,onDragClass:a.options.onDragClass,onDrop:a.onDrop,onDragStart:a.options.onReorderRowsDrag,dragHandle:a.options.dragHandle})}},c.prototype.onDrop=function(b,c){for(var d=a(b),e=d.data('bootstrap.table'),f=d.data('bootstrap.table').options,g=null,h=[],j=0;j<b.tBodies[0].rows.length;j++)g=a(b.tBodies[0].rows[j]),h.push(f.data[g.data('index')]),g.data('index',j).attr('data-index',j);f.data=f.data.slice(0,e.pageFrom-1).concat(h).concat(f.data.slice(e.pageTo)),f.onReorderRowsDrop.apply(b,[b,c]),e.trigger('reorder-row',h)}})(jQuery)});