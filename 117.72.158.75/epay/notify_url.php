<?php
@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");
$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyNotify();

if($verify_result) {
	$out_trade_no = $_GET['out_trade_no'];
	$trade_no = $_GET['trade_no'];
	$trade_status = $_GET['trade_status'];
    $money=$_GET['money'];
	$type = $_GET['type'];

    $sql = "SELECT * FROM qingka_wangke_pay WHERE out_trade_no=? LIMIT 1";
    $srow = $DB->prepare_getrow($sql, [$out_trade_no]);

    if ($_GET['trade_status'] == 'TRADE_SUCCESS' && $srow['status']==0 && $srow['money']==$money) {
		$sql = "update `qingka_wangke_pay` set `status` ='1',`endtime` =?,`trade_no`=? where `out_trade_no`=?";
        $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
        echo "success";
    }else{
		$sql = "update `qingka_wangke_pay` set `endtime` =?,`trade_no`=? where `out_trade_no`=?";
        $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
	    echo "success";
	}
}
else {
	echo "fail";
}
?>