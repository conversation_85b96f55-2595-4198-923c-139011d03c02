<?php  

function wkname()
{
	$data = array(
	    "29" => "29",
	    "bdkj" => "暗网",
	    "yyy" => "yyy教育"
	    );
	return $data;
}

function addWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];
	$b = $DB->get_row("select * from qingka_wangke_class where cid='{$cid}' ");
	$hid = $b["docking"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];

	// yyy教育下单接口
	if ($type == "yyy") {
		// 先登录获取token
		$login_data = array(
			"username" => $a["user"],
			"password" => $a["pass"]
		);
		$login_url = $a["url"] . "/api/login";
		$headers = array(
			"Content-Type: application/json",
			"Accept: application/json, text/plain, */*",
			"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"X-Requested-With: XMLHttpRequest"
		);
		$login_result = get_url3($login_url, json_encode($login_data), "", $headers);
		$login_response = json_decode($login_result, true);

		if ($login_response['code'] != 200) {
			return array("code" => -1, "msg" => "登录失败: " . $login_response['message']);
		}

		$access_token = $login_response['data']['accessToken'];
		$refresh_token = $login_response['data']['refreshToken'];

		// 构建Cookie
		$token_data = array(
			"accessToken" => $access_token,
			"expires" => 1919520000000,
			"refreshToken" => $refresh_token
		);
		$token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
		$encoded_token = urlencode($token_json);
		$cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

		// 提交订单 - 使用空格分隔账号、密码、课程名称
		$order_data = array(
			"lastoid" => $noun, // 使用传入的noun作为lastoid
			"orderData" => $user . " " . $pass . " " . $kcname, // 空格分隔
			"orderNote" => "",
			"search" => "0" // 0表示下单，1表示查课
		);

		$order_headers = array(
			"Content-Type: application/json",
			"Accept: application/json, text/plain, */*",
			"Authorization: Bearer " . $access_token,
			"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"X-Requested-With: XMLHttpRequest"
		);

		$order_url = $a["url"] . "/api/order";
		$order_result = get_url3($order_url, json_encode($order_data), $cookie, $order_headers);
		$order_response = json_decode($order_result, true);

		if ($order_response && isset($order_response['code'])) {
			if ($order_response['code'] == 200) {
				// yyy教育下单成功后需要获取订单ID，可能需要查询订单列表获取最新订单
				return array(
					"code" => 1,
					"msg" => "下单成功",
					"yid" => $oid // 暂时使用本地订单ID，后续可以通过查询订单列表获取上游订单ID
				);
			} else {
				return array("code" => -1, "msg" => $order_response['message'] ?? "下单失败");
			}
		} else {
			return array("code" => -1, "msg" => "下单接口返回数据异常");
		}
	}

	/*****
	 自己可以根据规则增加下单接口

	//XXXX下单接口
	else if ($type == "XXXX") {
	$data = array("optoken" => $token,"type" => $noun);  请求体参数自己加
	$XXXX_ul = $a["url"];      变量XXXX自己命名    获取顶级域名
	$XXXX_dingdan = "http://$XXXX_ul/api/CourseQuery/api/";    请求接口   XXXX自己命名
	$result = get_url($XXXX_dingdan, $data, $cookie);
	$result = json_decode($result, true);

	if ($result["code"] == "0") {
		$b = array("code" => 1, "msg" => $result["msg"]);
	} else {
		$b = array("code" => -1, "msg" => $result["msg"]);
	}
	return $b;
    }


	$token  传的token
	$school  传的学校
	$user    传的账号
	$pass    传的密码
	$noun    传的平台里面的接口编号
	$kcid    传的课程id
	****/
    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=add";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        if ($result["code"] == "0") {
        $b = array("code" => 1, "msg" => "下单成功");
        } else {
        $b = array("code" => -1, "msg" => $result["msg"]);
        }
    return $b;
    }
          if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=add";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       if ($result["code"] == "0") {
          	return ['code'=> 1,'msg'=>'对接成功，id:'.$result['id'],'yid'=>$result['id']];
       } else {
          $b = array("code" => - 1, "msg" => $result["msg"]);
       }
       return $b;
    }
	else{
	    print_r("没有了,文件xdjk.php,可能故障：参数缺少，比如平台名错误！！！");die;
	}
	
}


?>