<?php
$title='系统设置';
require_once('head.php');
if($userrow['uid']!=1){
setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
exit("<script language='javascript'>window.location.href='login.php';</script>");}
?>
     <div class="app-content-body ">
        <div class="wrapper-md control" id="add">
	       <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 6px;">
				<div class="panel-body">
					<form class="form-horizontal devform" id="form-web">
					    <div  class="card">
          <ul class="nav nav-tabs" role="tablist">
            <li class="active">
              <a data-toggle="tab" href="#wzpz">网站配置</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#dlpz">代理配置</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#zfpz">支付配置</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#flpz">分类配置</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#ckpz">api调用本站配置</a>
            </li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane fade active in" id="wzpz">
		            <div class="form-group">
							<label class="col-sm-2 control-label">站点名字</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="sitename" value="<?=$conf['sitename']?>" placeholder="请输入站点名字" required>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label">SEO关键词</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="keywords" value="<?=$conf['keywords']?>" placeholder="请输入站点名字" required>
							</div>
						</div>
												
						<div class="form-group">
							<label class="col-sm-2 control-label">SEO介绍</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="description" value="<?=$conf['description']?>" placeholder="请输入站点名字" required>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">主页LOGO地址</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="logo" value="<?=$conf['logo']?>" placeholder="请输入logo地址" required>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">是否开启水印</label>
								<div class="col-sm-9">
									<select name="sykg" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">	
	                            	   <option value="1" <?php if($conf['sykg']==1){ echo 'selected';}?>>1_允许</option>   
	                            	   <option value="0" <?php if($conf['sykg']==0){ echo 'selected';}?>>0_拒绝</option>                           	                            	
	                                </select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">弹窗公告notice内容</label>
								<div class="col-sm-9">
									<textarea type="text" name="notice" class="layui-textarea"  rows="5"><?=$conf['notice']?></textarea>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">弹窗公告tcgonggao内容</label>
								<div class="col-sm-9">
									<textarea type="text" name="tcgonggao" class="layui-textarea"  rows="5"><?=$conf['tcgonggao']?></textarea>
							</div>
						</div>
            </div>
            <div class="tab-pane fade" id="dlpz">
                <div class="form-group">
							<label class="col-sm-2 control-label">是否开启上级迁移功能</label>
								<div class="col-sm-9">
									<select name="sjqykg" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">	
	                            	   <option value="1" <?php if($conf['sjqykg']==1){ echo 'selected';}?>>1_开启</option>   
	                            	   <option value="0" <?php if($conf['sjqykg']==0){ echo 'selected';}?>>0_关闭</option>                           	                            	
	                                </select>
							</div>
						</div>
                <div class="form-group">
							<label class="col-sm-2 control-label">是否允许邀请码注册</label>
								<div class="col-sm-9">
									<select name="user_yqzc" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">
	                            	    <option value="1" <?php if($conf['user_yqzc']==1){ echo 'selected';}?>>1_允许</option> 
	                            	    <option value="0" <?php if($conf['user_yqzc']==0){ echo 'selected';}?>>0_拒绝</option>                           	                            	
	                                </select>
							   </div>
						</div>	

						<div class="form-group">
							<label class="col-sm-2 control-label">是否允许后台开户</label>
								<div class="col-sm-9">
									<select name="user_htkh" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">	
	                            	   <option value="1" <?php if($conf['user_htkh']==1){ echo 'selected';}?>>1_允许</option>   
	                            	   <option value="0" <?php if($conf['user_htkh']==0){ echo 'selected';}?>>0_拒绝</option>                           	                            	
	                                </select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-sm-2 control-label">后台开户开户费用</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="user_ktmoney" value="<?=$conf['user_ktmoney']?>" placeholder="" required>
							</div>
						</div>
            </div>
            <div class="tab-pane fade" id="zfpz">
                <div class="form-group">
							<label class="col-sm-2 control-label">最低充值</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="zdpay" value="<?=$conf['zdpay']?>" placeholder="请输入你的商户KEY" required>
							</div>
						</div>
					    <div class="form-group">
							<label class="col-sm-2 control-label">是否开启QQ支付</label>
								<div class="col-sm-9">
									<select name="is_qqpay" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">
	                            	    <option value="1" <?php if($conf['is_qqpay']==1){ echo 'selected';}?>>1_开启</option> 
	                            	    <option value="0" <?php if($conf['is_qqpay']==0){ echo 'selected';}?>>0_关闭</option>                           	                            	
	                                </select>
							   </div>
						</div>	
						<div class="form-group">
							<label class="col-sm-2 control-label">是否开启微信支付</label>
								<div class="col-sm-9">
									<select name="is_wxpay" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">
	                            	    <option value="1" <?php if($conf['is_wxpay']==1){ echo 'selected';}?>>1_开启</option> 
	                            	    <option value="0" <?php if($conf['is_wxpay']==0){ echo 'selected';}?>>0_关闭</option>                           	                            	
	                                </select>
							   </div>
						</div>	
						<div class="form-group">
							<label class="col-sm-2 control-label">是否开启支付宝支付</label>
								<div class="col-sm-9">
									<select name="is_alipay" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">
	                            	    <option value="1" <?php if($conf['is_alipay']==1){ echo 'selected';}?>>1_开启</option> 
	                            	    <option value="0" <?php if($conf['is_alipay']==0){ echo 'selected';}?>>0_关闭</option>                           	                            	
	                                </select>
							   </div>
						</div>	
						<div class="form-group">
							<label class="col-sm-2 control-label">易支付API</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="epay_api" value="<?=$conf['epay_api']?>" placeholder="格式：http://www.baidu.com/" required>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label">商户ID</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="epay_pid" value="<?=$conf['epay_pid']?>" placeholder="请输入你的商户ID" required>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">商户KEY</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="epay_key" value="<?=$conf['epay_key']?>" placeholder="请输入你的商户KEY" required>
							</div>
						</div>
            </div>
            <div class="tab-pane fade" id="flpz">
						<div class="form-group">
							<label class="col-sm-2 control-label">小猿提交分类类型</label>
								<div class="col-sm-9">
									<select name="fllx" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">
	                            	    <option value="1" <?php if($conf['fllx']==1){ echo 'selected';}?>>一框式搜索选择</option> 
	                            	    <option value="2" <?php if($conf['fllx']==2){ echo 'selected';}?>>按钮式直接展示</option>
	                            	    <option value="3" <?php if($conf['fllx']==3){ echo 'selected';}?>>不启用分类</option>
	                            	                 	                            	
	                                </select>
							   </div>
						</div>	
            </div>
            <div class="tab-pane fade" id="ckpz">
                        <div class="form-group">
							<label class="col-sm-2 control-label">是否开启API调用功能</label>
								<div class="col-sm-9">
									<select name="settings" class="layui-select" style="    background: url('../assets/img/arrow.png') no-repeat scroll 99%;   width:100%">
	                            	    <option value="1" <?php if($conf['settings']==1){ echo 'selected';}?>>开启API调用</option> 
	                            	    <option value="0" <?php if($conf['settings']==0){ echo 'selected';}?>>关闭API调用</option>
	                                </select>
							   </div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">chadan2接口请求自动同步概率</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="api_tongb" value="<?=$conf['api_tongb']?>" placeholder="输入1代表每次都请求上游一次，10代表同步概率为1/10，推荐2、3" required>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">API调用扣费限制(单位:%)</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="api_proportion" value="<?=$conf['api_proportion']?>" placeholder="请输入API调用比例扣费限制比例（单位：%）" required>
							</div>
						</div>
                        <div class="form-group">
							<label class="col-sm-2 control-label">API查课余额限制(单位:元)</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="api_ck" value="<?=$conf['api_ck']?>" placeholder="请输入API查课余额限制金额" required>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">API下单余额限制(单位:元)</label>
								<div class="col-sm-9">
									<input type="text" class="layui-input" name="api_xd" value="<?=$conf['api_xd']?>" placeholder="请输入API下单余额限制金额" required>
							</div>
						</div>
						</div>
            </div>
            
          </div>
																									
				  	    <div class="col-sm-offset-2 col-sm-4">
				  	    	<input type="button" @click="add" value="立即修改" class="layui-btn"/>
				  	    </div>

			        </form>
			      

		        </div>
	     </div>
      </div>
    </div>

<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>

<script>
new Vue({
    el:"#add",
    data:{

    },
    methods:{
        add:function(){
            var loading=layer.load(2);
            
            // 创建 FormData 对象
            var formData = new FormData();
            formData.append('data', $("#form-web").serialize());
            
            // 使用 axios 发送 POST 请求
            axios.post("/apiadmin.php?act=webset", formData)
            .then(function(response){
                layer.close(loading);
                if(response.data.code==1){
                    layer.alert(response.data.msg,{icon:1,title:"温馨提示"},function(){
                        setTimeout(function(){
                            window.location.href=""
                        }, 1000);
                    });
                }else{
                    layer.alert(response.data.msg,{icon:2,title:"温馨提示"});
                }
            })
            .catch(function(error){
                layer.close(loading);
                layer.alert("请求失败",{icon:2,title:"温馨提示"});
                console.error(error);
            });
        }   
    },
    mounted(){
        //this.getclass();		
    }
});
</script>