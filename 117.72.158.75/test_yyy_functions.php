<?php
/**
 * yyy教育查课下单功能测试脚本
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>yyy教育功能测试</title></head><body>";
echo "<h1>yyy教育查课下单功能测试</h1>";

// 包含必要文件
try {
    include 'confing/mysqlset.php';
    include 'Checkorder/configuration.php';
    echo "<p style='color:green'>✅ 配置文件加载成功</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 配置文件加载失败: " . $e->getMessage() . "</p>";
    exit;
}

// 创建数据库连接
try {
    $mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
    if ($mysqli->connect_error) {
        throw new Exception("连接失败: " . $mysqli->connect_error);
    }
    $mysqli->set_charset("utf8");
    echo "<p style='color:green'>✅ 数据库连接成功</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    exit;
}

// 模拟DB类
class SimpleDB {
    private $mysqli;
    
    public function __construct($mysqli) {
        $this->mysqli = $mysqli;
    }
    
    public function get_row($sql) {
        $result = $this->mysqli->query($sql);
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        return false;
    }
}

$DB = new SimpleDB($mysqli);

// 获取yyy教育的货源和商品信息
echo "<h2>📋 获取测试数据</h2>";

$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'yyy' LIMIT 1");
if (!$huoyuan) {
    echo "<p style='color:red'>❌ 未找到yyy教育货源配置</p>";
    exit;
}
echo "<p style='color:green'>✅ 找到货源: {$huoyuan['name']} (ID: {$huoyuan['hid']})</p>";

$class = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE name LIKE '%yyy教育%' AND status = 1 LIMIT 1");
if (!$class) {
    echo "<p style='color:red'>❌ 未找到yyy教育商品</p>";
    exit;
}
echo "<p style='color:green'>✅ 找到商品: {$class['name']} (ID: {$class['cid']}, noun: {$class['noun']})</p>";

// 不直接包含ckjk.php，而是复制其中的yyy教育查课逻辑
// 避免函数重复声明的问题

// 测试查课功能
echo "<h2>🔍 测试查课功能</h2>";

$test_school = "测试学校";
$test_user = "testuser123";
$test_pass = "testpass123";

echo "<p>测试参数:</p>";
echo "<ul>";
echo "<li>货源ID: {$huoyuan['hid']}</li>";
echo "<li>商品标识: {$class['noun']}</li>";
echo "<li>学校: {$test_school}</li>";
echo "<li>账号: {$test_user}</li>";
echo "<li>密码: {$test_pass}</li>";
echo "</ul>";

// 自定义查课函数，避免包含冲突
function testYyyGetWk($hid, $noun, $school, $user, $pass) {
    global $DB;

    $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid = '$hid' LIMIT 1");
    if (!$a) {
        return array("code" => -1, "msg" => "货源不存在");
    }

    $type = $a["pt"];
    if ($type != "yyy") {
        return array("code" => -1, "msg" => "不是yyy教育货源");
    }

    // yyy教育查课逻辑
    $login_data = array(
        "username" => $a["user"],
        "password" => $a["pass"]
    );
    $login_url = $a["url"] . "/api/login";
    $headers = array(
        "Content-Type: application/json",
        "Accept: application/json, text/plain, */*",
        "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "X-Requested-With: XMLHttpRequest"
    );
    $login_result = get_url3($login_url, json_encode($login_data), "", $headers);
    $login_response = json_decode($login_result, true);

    if ($login_response['code'] != 200) {
        return array("code" => -1, "msg" => "登录失败: " . $login_response['message']);
    }

    $access_token = $login_response['data']['accessToken'];
    $refresh_token = $login_response['data']['refreshToken'];

    // 构建Cookie
    $token_data = array(
        "accessToken" => $access_token,
        "expires" => 1919520000000,
        "refreshToken" => $refresh_token
    );
    $token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
    $encoded_token = urlencode($token_json);
    $cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

    // 查询课程
    $order_data = array(
        "lastoid" => $noun,
        "orderData" => $user . "\t" . $pass,
        "orderNote" => "",
        "search" => "1"
    );

    $query_headers = array(
        "Content-Type: application/json",
        "Accept: application/json, text/plain, */*",
        "Authorization: Bearer " . $access_token,
        "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "X-Requested-With: XMLHttpRequest"
    );

    $query_url = $a["url"] . "/api/order";
    $query_result = get_url3($query_url, json_encode($order_data), $cookie, $query_headers);
    $query_response = json_decode($query_result, true);

    if ($query_response['code'] != 200) {
        return array("code" => -1, "msg" => "查课失败: " . ($query_response['message'] ?? "未知错误"));
    }

    // 解析课程数据
    $courses = array();
    if (isset($query_response['data']) && is_array($query_response['data'])) {
        foreach ($query_response['data'] as $course_info) {
            if (strpos($course_info, "----") !== false) {
                $parts = explode("----", $course_info);
                if (count($parts) >= 3) {
                    $courses[] = array(
                        'name' => $parts[2],
                        'account' => $parts[0],
                        'password' => $parts[1],
                        'course_name' => $parts[2],
                        'full_info' => $course_info,
                        'user' => $parts[0],
                        'pass' => $parts[1],
                        'kcname' => $parts[2]
                    );
                }
            } else {
                $courses[] = array(
                    'name' => $course_info,
                    'course_name' => $course_info,
                    'kcname' => $course_info,
                    'full_info' => $course_info
                );
            }
        }
    }

    if (empty($courses)) {
        return array("code" => -1, "msg" => "未找到课程信息或账号密码错误");
    }

    return array(
        "code" => 1,
        "msg" => "查询成功",
        "data" => $courses
    );
}

try {
    $result = testYyyGetWk($huoyuan['hid'], $class['noun'], $test_school, $test_user, $test_pass);
    
    echo "<h3>查课结果:</h3>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    if ($result && isset($result['code'])) {
        if ($result['code'] == 1) {
            echo "<p style='color:green'>✅ 查课接口调用成功</p>";
            if (isset($result['data']) && is_array($result['data'])) {
                echo "<p>找到课程数量: " . count($result['data']) . "</p>";
                foreach ($result['data'] as $index => $course) {
                    echo "<p>课程 " . ($index + 1) . ": ";
                    if (is_array($course)) {
                        echo htmlspecialchars($course['name'] ?? $course['course_name'] ?? '未知课程');
                    } else {
                        echo htmlspecialchars($course);
                    }
                    echo "</p>";
                }
            }
        } else {
            echo "<p style='color:orange'>⚠️ 查课返回: {$result['msg']}</p>";
            echo "<p>这可能是因为测试账号不存在，属于正常情况</p>";
        }
    } else {
        echo "<p style='color:red'>❌ 查课接口返回格式异常</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>❌ 查课测试异常: " . $e->getMessage() . "</p>";
}

// 测试下单功能
echo "<h2>📝 测试下单功能</h2>";

// 首先创建一个测试订单
$test_order_data = array(
    'uid' => 1,
    'cid' => $class['cid'],
    'hid' => $huoyuan['hid'],
    'ptname' => $class['name'],
    'school' => $test_school,
    'name' => '测试用户',
    'user' => $test_user,
    'pass' => $test_pass,
    'kcid' => '',
    'kcname' => '测试课程',
    'courseEndTime' => '',
    'fees' => 1.00,
    'noun' => $class['noun'],
    'miaoshua' => 0,
    'addtime' => date('Y-m-d H:i:s'),
    'ip' => '127.0.0.1',
    'dockstatus' => '0',
    'status' => '待处理',
    'yid' => '',
    'process' => '',
    'remarks' => '',
    'bsnum' => 0
);

// 插入测试订单
$insert_sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus, status, yid, process, remarks, bsnum) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = $mysqli->prepare($insert_sql);
$stmt->bind_param("iissssssssdssisssssi", 
    $test_order_data['uid'], $test_order_data['cid'], $test_order_data['hid'], $test_order_data['ptname'],
    $test_order_data['school'], $test_order_data['name'], $test_order_data['user'], $test_order_data['pass'],
    $test_order_data['kcid'], $test_order_data['kcname'], $test_order_data['courseEndTime'], $test_order_data['fees'],
    $test_order_data['noun'], $test_order_data['miaoshua'], $test_order_data['addtime'], $test_order_data['ip'],
    $test_order_data['dockstatus'], $test_order_data['status'], $test_order_data['yid'], $test_order_data['process'],
    $test_order_data['remarks'], $test_order_data['bsnum']
);

if ($stmt->execute()) {
    $test_oid = $mysqli->insert_id;
    echo "<p style='color:green'>✅ 创建测试订单成功 (ID: {$test_oid})</p>";
    
    // 自定义下单函数，避免包含冲突
    function testYyyAddWk($oid) {
        global $DB;

        $d = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid = '$oid' LIMIT 1");
        if (!$d) {
            return array("code" => -1, "msg" => "订单不存在");
        }

        $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid = '{$d['hid']}' LIMIT 1");
        if (!$a) {
            return array("code" => -1, "msg" => "货源不存在");
        }

        $type = $a["pt"];
        if ($type != "yyy") {
            return array("code" => -1, "msg" => "不是yyy教育货源");
        }

        // yyy教育下单逻辑
        $login_data = array(
            "username" => $a["user"],
            "password" => $a["pass"]
        );
        $login_url = $a["url"] . "/api/login";
        $headers = array(
            "Content-Type: application/json",
            "Accept: application/json, text/plain, */*",
            "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "X-Requested-With: XMLHttpRequest"
        );
        $login_result = get_url3($login_url, json_encode($login_data), "", $headers);
        $login_response = json_decode($login_result, true);

        if ($login_response['code'] != 200) {
            return array("code" => -1, "msg" => "登录失败: " . $login_response['message']);
        }

        $access_token = $login_response['data']['accessToken'];
        $refresh_token = $login_response['data']['refreshToken'];

        // 构建Cookie
        $token_data = array(
            "accessToken" => $access_token,
            "expires" => 1919520000000,
            "refreshToken" => $refresh_token
        );
        $token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
        $encoded_token = urlencode($token_json);
        $cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

        // 提交订单
        $order_data = array(
            "lastoid" => $d['noun'],
            "orderData" => $d['user'] . " " . $d['pass'] . " " . $d['kcname'],
            "orderNote" => "",
            "search" => "0"
        );

        $order_headers = array(
            "Content-Type: application/json",
            "Accept: application/json, text/plain, */*",
            "Authorization: Bearer " . $access_token,
            "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "X-Requested-With: XMLHttpRequest"
        );

        $order_url = $a["url"] . "/api/order";
        $order_result = get_url3($order_url, json_encode($order_data), $cookie, $order_headers);
        $order_response = json_decode($order_result, true);

        if ($order_response && isset($order_response['code'])) {
            if ($order_response['code'] == 200) {
                return array(
                    "code" => 1,
                    "msg" => "下单成功",
                    "yid" => $oid
                );
            } else {
                return array("code" => -1, "msg" => $order_response['message'] ?? "下单失败");
            }
        } else {
            return array("code" => -1, "msg" => "下单接口返回数据异常");
        }
    }
    
    try {
        $order_result = testYyyAddWk($test_oid);
        
        echo "<h3>下单结果:</h3>";
        echo "<pre>" . json_encode($order_result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
        if ($order_result && isset($order_result['code'])) {
            if ($order_result['code'] == 1) {
                echo "<p style='color:green'>✅ 下单接口调用成功</p>";
                if (isset($order_result['yid'])) {
                    echo "<p>上游订单ID: {$order_result['yid']}</p>";
                }
            } else {
                echo "<p style='color:orange'>⚠️ 下单返回: {$order_result['msg']}</p>";
            }
        } else {
            echo "<p style='color:red'>❌ 下单接口返回格式异常</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color:red'>❌ 下单测试异常: " . $e->getMessage() . "</p>";
    }
    
    // 清理测试订单
    $mysqli->query("DELETE FROM qingka_wangke_order WHERE oid = {$test_oid}");
    echo "<p style='color:blue'>🗑️ 测试订单已清理</p>";
    
} else {
    echo "<p style='color:red'>❌ 创建测试订单失败: " . $stmt->error . "</p>";
}

// 测试建议
echo "<h2>💡 测试建议</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;'>";
echo "<h3>如何进行真实测试:</h3>";
echo "<ol>";
echo "<li><strong>使用真实账号密码</strong>：将上面的测试账号密码替换为真实的学员账号密码</li>";
echo "<li><strong>选择正确的项目</strong>：确保选择的yyy教育项目ID(noun)与学员账号匹配</li>";
echo "<li><strong>检查网络连接</strong>：确保服务器能访问yyy教育的API地址</li>";
echo "<li><strong>查看错误日志</strong>：如果失败，查看具体的错误信息</li>";
echo "</ol>";
echo "</div>";

// 常见问题排查
echo "<h2>🔧 常见问题排查</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
echo "<h3>如果查课失败:</h3>";
echo "<ul>";
echo "<li>检查账号密码是否正确</li>";
echo "<li>检查项目ID(noun)是否正确</li>";
echo "<li>检查yyy教育API是否可访问</li>";
echo "<li>检查货源配置中的账号密码是否正确</li>";
echo "</ul>";
echo "<h3>如果下单失败:</h3>";
echo "<ul>";
echo "<li>确保先查课成功</li>";
echo "<li>检查课程名称格式是否正确</li>";
echo "<li>检查订单数据是否完整</li>";
echo "</ul>";
echo "</div>";

$mysqli->close();
echo "</body></html>";
?>
