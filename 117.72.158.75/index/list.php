<?php
$mod='blank';
$title='订单列表';
$nsgsb='123';
require_once('head.php');
?>
     <div class="app-content-body ">
        <div class="wrapper-md control">
        <div class="panel panel-default" id="orderlist" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">订单列表</div>
                 <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                 	<div class="form-horizontal devform">	
                 		   <div class="el-form layui-row layui-col-space10">
    <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
    <el-select id="select" v-model="cx.cid" filterable placeholder="项目名称" style="width:100%">
        <el-option label="项目名称" value=""></el-option>
        <?php
            $a = $DB->query("select * from qingka_wangke_class where status!=2 ");
            while ($row = $DB->fetch($a)) {
                $shortName = mb_strlen($row['name']) > 25 ? mb_substr($row['name'], 0, 25) . '...' : $row['name'];
                echo '<el-option label="' . $shortName . '" value="' . $row['cid'] . '"></el-option>';
            }
        ?>
    </el-select>
</div>
                              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">	
                 		   				<el-select id="select" v-model="cx.status_text" filterable placeholder="订单状态" style="width:100%">
                 		   				    <el-option label="订单状态" value=""></el-option>
                 		   				    <el-option label="待处理" value="待处理"></el-option>
                 		   				    <el-option label="进行中" value="进行中"></el-option>
                 		   				    <el-option label="已完成" value="已完成"></el-option>
                 		   				    <el-option label="补刷中" value="补刷中"></el-option>
                 		   				    <el-option label="异常" value="异常"></el-option>
                 		   				</el-select>	 
                              </div>
                              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">	
                 		   				<el-select id="select" v-model="cx.dock" filterable placeholder="处理状态" style="width:100%">
                 		   				    <el-option label="提交状态" value=""></el-option>
                 		   				    <el-option label="提交成功" value="1"></el-option>
                 		   				    <el-option label="等待提交" value="0"></el-option>
                 		   				    <el-option label="提交失败" value="2"></el-option>
                 		   				    <el-option label="重复提交" value="3"></el-option>
                 		   				    <el-option label="已经取消" value="4"></el-option>
                 		   				    <el-option label="提交中断" value="5"></el-option>
                 		   				</el-select>	 
                                </div>
                             <div class="layui-col-md3 layui-col-sm3 layui-col-xs9">
                            <el-date-picker
                                v-model="cx.dateRange"
                                type="datetimerange"
                                range-separator="~"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                style="width:100%"
                            >
                                        </el-date-picker>
                                 </div>
                                <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">	
                 		   				<el-select id="select" v-model="cx.pagesize" filterable placeholder="50条/页" style="width:100%">
                 		   				    <el-option label="50条/页" value="50"></el-option>
                 		   				    <el-option label="100条/页" value="100"></el-option>
                 		   				    <el-option label="200条/页" value="200"></el-option>
                 		   				    <el-option label="300条/页" value="300"></el-option>
                 		   				    <el-option label="400条/页" value="400"></el-option>
                 		   				    <el-option label="1000条/页（慎用）" value="1000"></el-option>
                 		   				    <el-option v-if="row.uid==1" label="5000条/页" value="5000"></el-option>
                 		   				</el-select>	 
                                    </div>
                            </div>
                        <div class="form-horizontal devform">	
                 	          <div class="layui-row layui-col-space10">
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">	
                 		   				<el-select id="select" v-model="cx.hid" filterable placeholder="所属渠道" style="width:100%">
        <el-option label="所属渠道" value=""></el-option>
        <?php
            $a = $DB->query("select * from qingka_wangke_huoyuan where status=1 ");
            while ($row = $DB->fetch($a)) {
                $shortName = strlen($row['name']) > 50 ? substr($row['name'], 0, 50) . '...' : $row['name'];
                echo '<el-option label="' . $shortName . '" value="' . $row['hid'] . '"></el-option>';
            }
        ?>
    </el-select>
</div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.school" placeholder="请输入学校关键字"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.qq" placeholder="请输入下单账号"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.status_text" placeholder="请输入状态关键字"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.kcname" placeholder="请输入下单课程名称"></el-input>
                 	              </div>
                 	              </div>
                 	              </div>
                 	          <div class="form-horizontal devform">	
                 	          <div class="layui-row layui-col-space10">
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.oid" placeholder="请输入订单ID"></el-input>
                 	              </div>
                 	               <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.mima" placeholder="请输入密码"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.remarks" placeholder="请输入进度"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.uid" placeholder="请输入用户UID"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3  layui-col-xs6" >
                                    <el-button type="primary" size="medium" icon="el-icon-search" value="查询" @click="get(1)" >查询</el-button>&nbsp;
                                    <el-button type="primary" size="medium" icon="el-icon-printer" value="导出" @click="showExportDialog" >导出</el-button>
                                 </div>
                         </div>
                         </div>
                      </div>
                      <br>
                        <div >
                            <el-collapse>
                                <el-collapse-item>
                                    <template slot="title"><i class="header-icon el-icon-edit"></i>&nbsp;修改任务显示状态</template>
                                    <div>
                                    <el-button type="warning" size="mini" icon="el-icon-time" @click="status_text('待处理')">待处理</el-button>
                                    <el-button type="success" size="mini" icon="el-icon-circle-check" @click="status_text('已完成')">已完成</el-button>
                                    <el-button type="primary" size="mini" icon="el-icon-loading" @click="status_text('进行中')">进行中</el-button>
                                    <el-button type="danger" size="mini" icon="el-icon-warning-outline" @click="status_text('异常')">异常</el-button>
                                    <el-button size="mini" icon="el-icon-circle-close" @click="status_text('已取消')">已取消</el-button>
                                </div>
                              </el-collapse-item>
                              <div v-if="row.uid==1" >
                              <el-collapse-item>
                                    <template slot="title"><i class="header-icon el-icon-edit"></i>&nbsp;处理状态操作</template>
                                        <div>
                                            <el-button type="warning" size="mini" @click="dock(0)">待处理</el-button>
                                            <el-button type="success" size="mini" @click="dock(1)">处理成功</el-button>
                                            <el-button type="danger" size="mini" @click="dock(2)">处理失败</el-button>
                                            <el-button type="info" size="mini" @click="dock(3)">重复下单</el-button>
                                            <el-button type="default" size="mini" @click="dock(4)">已取消</el-button>
                                            <el-button type="default" size="mini" @click="dock(99)">自营订单</el-button>
                                            <el-button type="danger" size="mini" @click="tk(sex)">订单退款</el-button><br><br>
                                            <el-button type="danger" size="mini" @click="sc(sex)">订单删除</el-button>
                                        </div>
                                </el-collapse-item>
                            </el-collapse>
                        </div>
                            <div >
                                <el-button size="mini" type="primary" @click="plzt(sex)" icon="el-icon-sort">批量更新</el-button>
                                <el-button size="mini" type="warning" @click="plbs(sex)" icon="el-icon-edit-outline">批量补单</el-button>
                                <el-button size="mini" type="info" @click="pltx(sex)" icon="el-icon-close">批量停止</el-button>
                                <el-button size="mini" type="danger" @click="plms(sex)" icon="el-icon-lightning">批量转秒</el-button>
<p>订单如有问题请自行检查后反馈即可,请不要重复下相同订单</p>
                                <!--
                                <a class="btn btn-xs btn btn-primary purple" id="checkboxAll" @click="selectAll()">全选</a>
                                <a class="btn btn-xs btn-info purple" @click="plzt(sex)"><i class="fa fa-send"></i>同步状态入队</a>
                                <a class="btn btn-xs btn-success purple" @click="plbs(sex)"><i class="fa fa-send"></i>补刷订单入队</a>-->
                            </div>
                          </el-collapse-item>
                        </el-collapse>
                      <br>
              <div class="el-table-column-fixed  table-responsive table-condensed" lay-size="sm" >
                <table class="table table-striped">
                  <thead style="white-space:nowrap"><tr><!--<th>#</th>-->
                  	<th><label class="lyear-checkbox checkbox-inline checkbox-info">
                           <input type="checkbox" id="checkboxAll"  @click="selectAll()"><span></span>
                       </label>
                    </th>
                  	<th>操作</th>
                  	<th>详细</th>
                  	<th>订单所属平台</th>
                  	<th>账号</th>
                  	<th>备注</th>
                  	<th>任务名称</th>
                  	<th>状态</th>
                  	<th>%</th>
                  	<th>订单详细信息</th>
                  	<th>时间</th>
                  	<th v-if="row.uid==1">状态</th>
                  	<th v-if="row.uid==1">UID</th>
                  	<th v-if="row.uid==1">扣费</th>
                  	</tr></thead>
                  <tbody>
                    <!--<a class="btn btn-xs btn-dark purplek"@click="plbs('待重刷')" >批量重刷</a>-->
                    <tr v-for="res in row.data">		            					
                                  <!--<td>
                                  	<span class="checkbox checkbox-success">
                          <input type="checkbox" id="checkboxAll" :value="res.oid" v-model="sex"><label for="checkbox1"></label></span>
                                  </td>-->
                 <td style="white-space:nowrap" >
                    <label class="lyear-checkbox checkbox-inline checkbox-info">
                    <input type="checkbox" id="checkboxAll" :value="res.oid" v-model="sex"><span v-if="row.uid==1"></span><span v-else>-</span>
                            </label>
                       </td>	
<td style="white-space:nowrap">
  <el-dropdown @command="handleCommand($event, res.oid)">
    <el-button type="primary" size="small" style="background-color: transparent; border: 1px solid #ccc; color: #6B6B6B;">
      操作<i class="el-icon-arrow-down el-icon--right"></i>
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="up">刷新</el-dropdown-item>
      <el-dropdown-item command="bs">补单</el-dropdown-item>
      <el-dropdown-item command="zt">停止</el-dropdown-item>
      <el-dropdown-item command="feedback">一键反馈</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</td>
                       <td><span class="layui-btn layui-btn-xs layui-btn-radius layui-btn-normal" @click="ddinfo(res)"><i class="layui-icon layui-icon-search"></i></span>
                      <span><button v-if="res.cid==12624" @click="jietu(res.user)" class="btn btn-xs btn-default" >截图</button></span>
                      <span><button v-if="res.cid==12624" @click="zhengshu(res.user)" class="btn btn-xs btn-primary" >证书</button></span>
                  </td>
                       <td style="white-space:nowrap, width: 300">{{res.ptname}}
                       <span v-if="res.miaoshua=='1'" style="color: red;">&nbsp;秒单</span>
                       </td>	            	      	
<td style="white-space:nowrap; width: 225">
  <button @click="copyToClipboard(res.school)" class="layui-btn layui-btn-xs  layui-btn-primary layui-border-black">复制学校</button>&nbsp;
  <span>{{ res.school }}</span><br>
  <button @click="copyToClipboard(res.user)" class="layui-btn layui-btn-xs  layui-btn-primary layui-border-black">复制账号</button>&nbsp;
  <span>{{ res.user }}</span><br>
  <button @click="copyToClipboard(res.pass)" class="layui-btn layui-btn-xs  layui-btn-primary layui-border-black">复制密码</button>&nbsp;
  <span>{{ res.pass }}</span><br>
  <button @click="copyAllToClipboard(res)" class="layui-btn layui-btn-xs layui-btn-primary layui-border-black">全部复制</button>&nbsp;
<span>备注：{{res.chapterCount}}</span>
</td>
                    	<td style="white-space:nowrap, width: 100">
                    	<span class="layui-btn layui-btn-xs layui-btn-radius layui-btn-normal" @click="tianjiabeizhu(res.oid)"><i class="layui-icon layui-icon-edit"></i></span>
                    	</td>   
                    	<td style="white-space:nowrap, width: 225">{{res.kcname}}</td>
                    	<td style="white-space:nowrap" @click="ddinfo(res)">
                    		<el-button v-if="res.status=='待处理'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待上号'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='排队中'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已暂停'" type="warning" size="mini" icon="el-icon-video-pause">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已停止'" type="warning" size="mini" icon="el-icon-error">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已完成'" type="success" size="mini" icon="el-icon-circle-check">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待更新'" type="warning" size="mini" icon="el-icon-download">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='异常'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='失败'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='密码错误'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已提取'" type="primary" size="mini" icon="el-icon-upload">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已提交'" type="primary" size="mini" icon="el-icon-upload">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='进行中'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='上号中'" type="primary" size="mini" icon="el-icon-monitor">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='考试中'" type="primary" size="mini" icon="el-icon-edit-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='队列中'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待考试'" type="primary" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待重启'" type="primary" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='等待下周'" type="primary" size="mini" icon="el-icon-alarm-clock">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='请自行处理'" type="success" size="mini" icon="el-icon-question">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='平时分'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='平时分中'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='补刷中'" type="info" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button  v-else style="color: white;"  type="info" size="mini" >{{res.status}}</el-button>
                    	</td>
                    	<td>{{res.process}}
                    	    <div class="layui-progress layui-progress-big" lay-showpercent="true">
                    	        <div class="layui-progress-bar layui-bg-blue"  lay-percent="res.process" v-bind:style="'width:'+(res.process)+';' ">
                    	        </div>
                    	    </div>
                    	    </td>
                    	<td style="white-space:nowrap, width: 400">{{res.remarks}}</td>         	
                    	<td style="white-space:nowrap, width: 180">{{res.addtime}}</td>
                    	<td style="white-space:nowrap" v-if="row.uid==1">
                    		<span @click="duijie(res.oid)" v-if="res.dockstatus==0" class="el-button el-button--warning el-button--mini">等待处理</span>
                    		<span v-if="res.dockstatus==1" class="el-button el-button--success el-button--mini">处理成功</span>
                    		<span @click="duijie(res.oid)" v-if="res.dockstatus==2" class="el-button el-button--danger el-button--mini">处理失败</span>
                    		<span v-if="res.dockstatus==3" class="el-button el-button--info el-button--mini">重复下单</span>
                    		<span v-if="res.dockstatus==4" class="el-button el-button--default is-plain el-button--mini">已取消</span>
                    		<span v-if="res.dockstatus==99" class="el-button el-button--default is-plain el-button--mini">自营订单</span></td>   
                        <td v-if="row.uid==1">{{res.uid}}</td>
                        <td style="white-space:nowrap" v-if="row.uid==1">{{res.fees}}</td>
                        </tr>
                  </tbody>
                </table>
                </div>
                 <ul class="pagination no-border" v-if="row.last_page>0" > 
                     <li><span>共 {{ row.total_count }} 条</span></li>
                     <li><a @click="get(1)">首页</a></li>
                     <li><a @click="row.current_page>1?get(row.current_page-1):''">&laquo;</a></li>
                     <li  @click="get(row.current_page-3)" v-if="row.current_page-3>=1"><a>{{ row.current_page-3 }}</a></li>
                     <li  @click="get(row.current_page-2)" v-if="row.current_page-2>=1"><a>{{ row.current_page-2 }}</a></li>
                     <li  @click="get(row.current_page-1)" v-if="row.current_page-1>=1"><a>{{ row.current_page-1 }}</a></li>
                     <li :class="{'active':row.current_page==row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
                     <li  @click="get(row.current_page+1)" v-if="row.current_page+1<=row.last_page"><a>{{ row.current_page+1 }}</a></li>
                     <li  @click="get(row.current_page+2)" v-if="row.current_page+2<=row.last_page"><a>{{ row.current_page+2 }}</a></li>
                     <li  @click="get(row.current_page+3)" v-if="row.current_page+3<=row.last_page"><a>{{ row.current_page+3 }}</a></li>		       			     
                     <li><a @click="row.last_page>row.current_page?get(row.current_page+1):''">&raquo;</a></li>
                     <li><a @click="get(row.last_page)">尾页</a></li>
                 </ul>

<div id="exportDialog" style="display: none; padding: 20px;">
    <div style="margin-bottom: 15px;">
        <label>导出格式：</label>
        <el-radio-group v-model="exportFormat">
            <el-radio label="txt">TXT文本</el-radio>
            <el-radio label="excel">Excel文件</el-radio>
        </el-radio-group>
    </div>
    <div style="margin-bottom: 15px;">
        <label>选择字段：</label>
        <el-checkbox-group v-model="exportFields">
            <el-checkbox label="school">学校</el-checkbox>
            <el-checkbox label="user">账号</el-checkbox>
            <el-checkbox label="pass">密码</el-checkbox>
            <el-checkbox label="kcname">课程名</el-checkbox>
            <el-checkbox label="status">状态</el-checkbox>
            <el-checkbox label="process">进度</el-checkbox>
            <el-checkbox label="remarks">备注</el-checkbox>
            <el-checkbox label="fees" v-if="row.uid==1">扣费</el-checkbox>
        </el-checkbox-group>
    </div>
    <div style="text-align: center; margin-top: 20px;">
        <el-button type="primary" @click="doExport" :disabled="exportFields.length === 0">确认导出</el-button>
        <el-button @click="closeExportDialog">取消</el-button>
    </div>
</div>
                <div id="ddinfo2" style="display: none;"><!--订单详情-->                    
                   <li class="list-group-item">
                   	<b>课程类型：</b>{{ddinfo3.info.ptname}}<span v-if="ddinfo3.info.miaoshua=='1'" style="color: red;">&nbsp;秒刷</span></li>
                   	<li class="list-group-item" style="word-break:break-all;"><b>账号信息：</b>{{ddinfo3.info.school}}&nbsp;{{ddinfo3.info.user}}&nbsp;{{ddinfo3.info.pass}}</li>
                   	<li class="list-group-item"><b>课程名字：</b>{{ddinfo3.info.kcname}}</li>
                   	<li class="list-group-item" v-if="ddinfo3.info.name!='null'"><b>学生姓名：</b>{{ddinfo3.info.name}}</li>
                   	<li class="list-group-item"><b>KCID：</b>{{ddinfo3.info.kcid}}</li>
                   	<li class="list-group-item"><b>站内反馈id：</b>{{ddinfo3.info.oid}}</li>
                   	<li class="list-group-item"><b>上游返回YID：</b>{{ddinfo3.info.yid}}</li>
                   	<li class="list-group-item"><b>下单时间：</b>{{ddinfo3.info.addtime}}</li>
                   	<li class="list-group-item"><b>上次同步时间：</b>{{ddinfo3.info.lastuptime}}</li>
                   	<li class="list-group-item" v-if="ddinfo3.info.courseStartTime"><b>课程开始时间：</b>{{ddinfo3.info.courseStartTime}}</li>
                   	<li class="list-group-item" v-if="ddinfo3.info.courseEndTime"><b>课程结束时间：</b>{{ddinfo3.info.courseEndTime}}</li>
                   	<li class="list-group-item" v-if="ddinfo3.info.examStartTime"><b>考试开始时间：</b>{{ddinfo3.info.examStartTime}}</li>
                   	<li class="list-group-item" v-if="ddinfo3.info.examEndTime"><b>考试结束时间：</b>{{ddinfo3.info.examEndTime}}</li>
                   	<li class="list-group-item"><b>订单状态：</b><span style="color: red;">{{ddinfo3.info.status}}</span>&nbsp;<button v-if="ddinfo3.info.dockstatus!='99'" @click="up(ddinfo3.info.oid)" class="el-button el-button--success is-plain el-button--mini"><li class="el-icon-refresh"></li>刷新</button>&nbsp;</li>
                   	<li class="list-group-item" v-if="ddinfo3.info.dockstatus!='4'"><b>操作：
                   	<button @click="ms(ddinfo3.info.oid)" class="btn btn-xs btn-danger">秒刷</button>&nbsp;
                   	<button @click="xgmm(ddinfo3.info.oid)" class="btn btn-xs btn-info">改密码</button>&nbsp;
                   	<button @click="bs(ddinfo3.info.oid)" class="btn btn-xs btn-primary">补单</button>&nbsp;
                    <button @click="quxiao(ddinfo3.info.oid)" class="btn btn-xs btn-default">取消</button></li>
                   	<li class="list-group-item"><b>进度：</b>{{ddinfo3.info.process}}<div class="progress">
                    	        <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="60" class="progress-bar progress-bar-info progress-bar-striped" role="progressbar" v-bind:style="'width:'+(ddinfo3.info.process)+';' ">
                    	        </div>
                    	    </div></li>
                   	<li class="list-group-item" v-if="ddinfo3.info.remarks"><b>备注：</b>{{ddinfo3.info.remarks}}</li>
              </div>
            </div>
          </div>
    </div>
   </div>
 </div>
<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
vm = new Vue({
    el: "#orderlist",
    data: {
        currentOid: '',
        row: [],
        timer: null,
        phone: '',
        list: '',
        sex: [],
        ddinfo3: {
            status: false,
            info: []
        },
        cx: {
            school: '',
            status_text: '',
            dock: '',
            qq: '',
            oid: '',
            uid: '',
            cid: '',
            kcname: '',
            pagesize: '50',
            remarks: '',
            mima: '',
            hid: '',
            dateRange: []
        },
        logData: [],
        exportScope: 'current',
        exportFormat: 'txt',
        exportFields: []
    },
    methods: {
        handleCommand(command, oid) {
            switch (command) {
                case 'up':
                    this.up(oid);
                    break;
                case 'zt':
                    this.zt(oid);
                    break;
                case 'bs':
                    this.bs(oid);
                    break;
                case 'feedback':
                    this.feedback(oid);
                    break;
                default:
                    break;
            }
        },
        async get(page) {
            try {
                let data = { cx: this.cx, page };
                var load = layer.load(2);
                const response = await this.$http.post("/apisub.php?act=orderlist", data, { emulateJSON: true });
                layer.close(load);
                if (response.data.code === 1) {
                    this.row = response.body;
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error(error);
            }
        },
        removepercent: function (text) {
            function isNumeric(value) {
                return !isNaN(parseFloat(value)) && isFinite(value) && typeof value !== 'boolean';
            }
            if (isNumeric(text.split('%').join(""))) {
                return text.split('%').join("");
            }
            return false;
        },
        showExportDialog: function () {
            if (this.row.data.length === 0 && this.exportScope === 'current') {
                layer.msg('当前页面没有数据可导出', { icon: 2 });
                return;
            }
            layer.open({
                type: 1,
                title: '导出选项',
                area: ['500px', '400px'],
                content: $('#exportDialog'),
                cancel: () => {
                    this.exportFields = []; // 清空字段选择
                }
            });
        },
        closeExportDialog: function () {
            layer.closeAll();
            this.exportFields = []; // 清空字段选择
        },
        doExport: function () {
            if (this.exportFields.length === 0) {
                layer.msg('请选择至少一个导出字段', { icon: 2 });
                return;
            }
                this.exportCurrentPage();
        },
        exportCurrentPage: function () {
            let headers = [];
            let data = [];

            // 根据选择的字段生成表头
            if (this.exportFields.includes('school')) headers.push('学校');
            if (this.exportFields.includes('user')) headers.push('账号');
            if (this.exportFields.includes('pass')) headers.push('密码');
            if (this.exportFields.includes('kcname')) headers.push('课程名');
            if (this.exportFields.includes('status')) headers.push('状态');
            if (this.exportFields.includes('process')) headers.push('进度');
            if (this.exportFields.includes('remarks')) headers.push('备注');
            if (this.exportFields.includes('fees')) headers.push('扣费');

            // 准备数据
            this.row.data.forEach(item => {
                let row = [];
                if (this.exportFields.includes('school')) row.push(item.school || '');
                if (this.exportFields.includes('user')) row.push(item.user || '');
                if (this.exportFields.includes('pass')) row.push(item.pass || '');
                if (this.exportFields.includes('kcname')) row.push(item.kcname || '');
                if (this.exportFields.includes('status')) row.push(item.status || '');
                if (this.exportFields.includes('process')) row.push(item.process || '');
                if (this.exportFields.includes('remarks')) row.push(item.remarks || '');
                if (this.exportFields.includes('fees')) row.push(item.fees || '');
                data.push(row);
            });

            this.generateExportFile(headers, data);
        },
        generateExportFile: function (headers, data) {
            if (this.exportFormat === 'txt') {
                this.exportAsTxt(headers, data);
            } else {
                this.exportAsExcel(headers, data);
            }
            layer.closeAll();
            this.exportFields = []; // 清空字段选择
        },
        exportAsTxt: function (headers, data) {
            let content = headers.join('\t') + '\n';
            data.forEach(row => {
                content += row.join('\t') + '\n';
            });

            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '订单数据_' + new Date().toLocaleDateString() + '.txt';
            link.click();
            URL.revokeObjectURL(url);
        },
        exportAsExcel: function (headers, data) {
            const wb = XLSX.utils.book_new();
            const wsData = [headers, ...data]; // 表头作为第一行，数据随后
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            XLSX.utils.book_append_sheet(wb, ws, '订单数据');
            XLSX.writeFile(wb, '订单数据_' + new Date().toLocaleDateString() + '.xlsx');
        },
        bs: function (oid) {
            layer.confirm('必须在订单出现异常的情况下使用<br>补刷不能提高订单进行速度<br>不正确补刷会造成不可预测的严重后果！<br>请问是否补刷所选的任务？', {
                title: '温馨提示',
                icon: 3,
                title: '安全警告',
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=bs&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        zt: function (oid) {
            layer.confirm('确定停止任务？<br>停止后可根据需要补单即可启动', {
                title: '温馨提示',
                icon: 3,
                title: '警告',
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=zt&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        up: function (oid) {
            var load = layer.load(2);
            $.get("/apisub.php?act=uporder&oid=" + oid, function (data) {
                layer.close(load);
                if (data.code == 1) {
                    vm.get(vm.row.current_page);
                    setTimeout(function () {
                        for (i = 0; i < vm.row.data.length; i++) {
                            if (vm.row.data[i].oid == oid) {
                                vm.ddinfo3.info = vm.row.data[i];
                                console.log(vm.row.data[i].oid);
                                console.log(vm.row.data[i].status);
                                console.log(vm.ddinfo3.info.status);
                                return true;
                            }
                        }
                    }, 1);
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            });
        },
        plzt: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要批量刷新？<br>新订单批量刷新没有作用，不要做傻事', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=plzt", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        plbs: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要入队补刷？<br>请先点击检查订单，未提交到上游的订单补刷后不退', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=plbs", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        plms: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要批量转秒？ ', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=plmiaoshua", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        pltx: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要批量停止？', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=pltx", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        duijie: function (oid) {
            layer.confirm('确定处理么?', {
                title: '温馨提示',
                icon: 3,
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=duijie&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        ms: function (oid) {
            layer.confirm('确定要执行该操作吗？需要扣除0.01费用。', {
                title: '温馨提示',
                icon: 3,
                title: '警告',
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=ms&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        xgmm: function (oid) {
            layer.prompt(
                { title: "修改密码（会扣除0.05积分）", formType: 3 },
                function (xgmm, index) {
                    layer.close(index);
                    var load = layer.load();
                    $.get("/apisub.php?act=xgmm&oid=" + oid, { xgmm }, function (data) {
                        layer.close(load);
                        if (data.code == 1) {
                            vm.get(vm.row.current_page);
                            layer.msg(data.msg, { icon: 1 })
                        } else {
                            layer.msg(data.msg, { icon: 2 });
                        }
                    });
                }
            );
        },
        quxiao: function (oid) {
            layer.confirm('你确定要取消订单吗？', {
                title: '警告：',
                icon: 3,
                btn: ['确定取消', '不用了'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=qx_order&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        status_text: function (a) {
            var load = layer.load(2);
            $.post("/apisub.php?act=status_order&a=" + a, { sex: this.sex, type: 1 }, { emulateJSON: true }).then(function (data) {
                layer.close(load);
                if (data.code == 1) {
                    vm.selectAll();
                    vm.get(vm.row.current_page);
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            });
        },
        dock: function (a) {
            var load = layer.load(2);
            $.post("/apisub.php?act=status_order&a=" + a, { sex: this.sex, type: 2 }, { emulateJSON: true }).then(function (data) {
                layer.close(load);
                if (data.code == 1) {
                    vm.selectAll();
                    vm.get(vm.row.current_page);
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            });
        },
        selectAll: function () {
            if (this.sex.length == 0) {
                for (i = 0; i < vm.row.data.length; i++) {
                    vm.sex.push(this.row.data[i].oid)
                }
            } else {
                this.sex = []
            }
        },
        ddinfo: function (a) {
            this.ddinfo3.info = a;
            var load = layer.load(2, { time: 300 });
            setTimeout(function () {
                layer.open({
                    type: 1,
                    title: '订单详情操作',
                    skin: 'layui-layer-demo',
                    closeBtn: 1,
                    anim: 2,
                    shadeClose: true,
                    content: $('#ddinfo2'),
                    end: function () {
                        $("#ddinfo2").hide();
                    }
                });
            }, 100);
        },
        tk: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要退款吗？陛下，三思三思！！！', {
                title: '温馨提示',
                icon: 3,
                btn: ['确定', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=tk", { sex: sex }, { emulateJSON: true }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        sc: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要删除此订单信息？', {
                title: '温馨提示',
                icon: 3,
                btn: ['确定', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=sc", { sex: sex }, { emulateJSON: true }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        feedback: function (oid) {
            var self = this;
            layer.prompt({
                title: '（当前：' + oid + ') 无效反馈扣服务费1.5/单',
                formType: 2,
                value: '老板帮忙看看这个（1什么问题）' + '，我（2通过什么方式了解到）确实是有这个问题，' + '（3如果有需要可以添加）客户说' + '，帮忙优先处理一下感谢老板！',
                area: ['350px', '250px'],
                btn: ['确定', '取消']
            }, function (feedbackText, index) {
                layer.close(index);
                feedbackText = feedbackText.trim();

                if (feedbackText === '') {
                    layer.msg('反馈内容不能为空', { icon: 2 });
                    return;
                }
                layer.confirm('承诺:全天多次不定时处理，数分钟至数小时内回复' + '<br>我确实遇到了订单上存在的难题√' + '<br>我已定位到该问题并非我个人原因造成√' + '<br>我已初步核查问题，保证反馈的问题绝对真实√' + '<br>若反馈无效问题，我自愿被扣除服务费1.5/单√' + '<br>我希望反馈的问题能尽快解决，请尽快处理。', {
                    icon: 3,
                    title: '二次确认',
                    btn: ['确定并提交反馈', '取消']
                }, function (index) {
                    layer.close(index);

                    var load = layer.load();
                    $.get("/mqgd.php?act=feedback&oid=" + oid, { feedback: feedbackText }, function (data) {
                        layer.close(load);
                        if (data.code === 1) {
                            layer.msg('反馈成功：' + data.msg, { icon: 1 });
                        } else {
                            layer.msg('ERROR' + data.msg, { icon: 2 });
                        }
                    });
                }, function (index) {
                    layer.close(index);
                });
            });
        },
        copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    layer.msg('已复制:' + text, { icon: 1 });
                }, (err) => {
                    layer.msg('复制失败: ' + err, { icon: 2 });
                });
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                layer.msg('已复制:' + text, { icon: 1 });
            }
        },
        copyAllToClipboard(res) {
            const combinedText = `${res.school} ${res.user} ${res.pass}`;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(combinedText).then(() => {
                    layer.msg('已复制全部信息: ' + combinedText, { icon: 1 });
                }, (err) => {
                    layer.msg('复制失败: ' + err, { icon: 2 });
                });
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = combinedText;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                layer.msg('已复制全部信息: ' + combinedText, { icon: 1 });
            }
        }
    },
    mounted() {
        this.get(1);
    }
});
</script>