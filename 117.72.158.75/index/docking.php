<?php
$mod='blank';
$title='对接文档';
require_once('head.php');
$fl=$DB->count("select addprice from qingka_wangke_user where uid='{$userrow['uid']}'");
/*$ck=$DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API查课' AND uid='{$userrow['uid']}' ");
$xd=$DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API添加任务' AND uid='{$userrow['uid']}' ");
$xdbxz=15;
$xdb=round($xd/$ck,4)*100;*/
?>
<link href="//unpkg.com/layui@2.8.6/dist/css/layui.css" rel="stylesheet">
	<div class="app-content-body">
	    <div class="wrapper-md control">		
			<div class="row">
				<div class="col-sm-12">
                <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">对接文档</div>
          <div class="card-body">
            <div class="row">
                                         <span style="color: #FF0033;">以下29 checkorder 部分代码仅限29使用 如果你是小储云 彩虹等平台 不要用addyqsl接口进行下单 会发生不可逆的错误！！！ [这个接口取消了','号分隔课程的代码部分，防止29提交因为有逗号的问题导致下单出错！但是在小储云等平台会出问题！请用你们对接插件原先自带的add接口就行了！]推荐对接方案：全部使用本站对接代码+使用批量进度套件 这样速度最快 效率最高 并且实时进度</span>		
                <div class="tab-content">
<pre class="layui-code code-demo" lay-title="getmoney接口">
api.php?act=getmoney
用途：查询余额
请求方法：post 内容格式 x-www-form-urlencoded
uid--您的UID--必传
key--您的KEY--必传
</pre>
<pre class="layui-code code-demo" lay-title="get接口">
api.php?act=get
用途：查课
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
uid--您的UID--必传
key--您的KEY--必传
platform--项目ID--必传
user--下单账号--必传
pass--下单密码--必传
school--用户学校--必传
</pre>
<pre class="layui-code code-demo" lay-title="add接口">
api.php?act=add
用途：普通下单
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
uid--您的UID--必传
key--您的KEY--必传
platform--项目ID--必传
user--下单账号--必传
pass--下单密码--必传
school--用户学校--必传
kcname--课程名称--必传
kcid--课程ID--非必传
score--分数--非必传
shichang--时长--非必传
</pre>
<pre class="layui-code code-demo" lay-title="addyqsl接口">
api.php?act=addyqsl
用途：29系统专用下单
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
uid--您的UID--必传
key--您的KEY--必传
platform--项目ID--必传
user--下单账号--必传
pass--下单密码--必传
school--用户学校--必传
kcname--课程名称--必传
kcid--课程ID--非必传
score--分数--非必传
shichang--时长--非必传
</pre>
<pre class="layui-code code-demo" lay-title="chadan接口">
api.php?act=chadan
用途：普通查单
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
username--下单账号[与订单ID二选一]--必传
yid--订单ID[与下单账号二选一]--必传
</pre>
<pre class="layui-code code-demo" lay-title="chadan2接口">
api.php?act=chadan2
用途：带更新的查单，访问这个接口以后会更新这个订单并输出订单信息
但是同步系统不会再为这个订单同步进度
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
yid--订单ID--必传
</pre>
<pre class="layui-code code-demo" lay-title="/api/search接口">
/api/search?
用途：查单，严谨匹配订单信息避免串进度
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
username--下单账号--必传
kcname--下单课程名--必传
cid--项目cid--必传
</pre>
<pre class="layui-code code-demo" lay-title="plchadan接口">
api.php?act=plchadan
用途：批量查单（请下载批量进度插件使用）
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
uid--您的UID-必传
key--您的KEY--必传
offset--数量--必传
timestamp-时间距离--必传
</pre>
<pre class="layui-code code-demo" lay-title="budan接口">
api.php?act=budan
用途：补单
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
uid--您的UID--必传
key--您的KEY--必传
id--订单ID--必传
</pre>
<pre class="layui-code code-demo" lay-title="zt接口">
api.php?act=zt
用途：暂停订单（部分有效，配合多功能插件使用）
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
id--订单ID--必传
</pre>
<pre class="layui-code code-demo" lay-title="ms接口">
api.php?act=ms
用途：转秒订单（部分有效，配合多功能插件使用）
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
id--订单ID--必传
</pre>
<pre class="layui-code code-demo" lay-title="xgmm接口">
api.php?act=xgmm
用途：修改订单密码（部分有效，配合多功能插件使用）
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
id--订单ID--必传
xgmm--新密码--必传
</pre>
<pre class="layui-code code-demo" lay-title="getclass接口">
api.php?act=getclass
用途：获取商品id/名称/价格/介绍/所在分类id/所在分类名称
请求方法：post 内容格式 x-www-form-urlencoded
请求内容：
uid--您的UID--必传
key--您的KEY--必传
</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder 标头部分，一般在jdjk.php">
"yqsl" => "yqsl",</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder ckjk.php部分">else if ($type == "yqsl") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=get";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }</pre>
<pre class="layui-code code-demo" lay-title="注意：分数时长传入可能跟你的不同，如不同请修改，也可以去掉">
"score" => $score, "shichang" => $shichang</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder xdjk.php部分">
    //注意是否有$score = $d["score"];和$shichang = $d["shichang"];代表分数时长
    else if ($type == "yqsl") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid  ,"score" => $score, "shichang" => $shichang);
        $dx_rl = $a["url"];
        $dx_url = "$dx_rl/api.php?act=addyqsl";
        $result = get_url($dx_url, $data);
        $result = json_decode($result, true);
        if ($result["code"] == "0") {
            $b = array("code" => 1, "msg" => $result["msg"]);
        } else {
            $b = array("code" => -1, "msg" => $result["msg"]);
        }
        return $b;
    }
</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder jdjk.php部分 新版">
    else if ($type == "yqsl") { 
    $uu_rl = $a["url"];
    if (!empty($d["yid"])) {
        $uu_url = "$uu_rl/api.php?act=chadan2"; 
        $data = array("yid" => $d["yid"]);
    } else {
        $uu_url = "$uu_rl/api/search?";
        $data = array("username" => $user,"kcname" => $kcname,"cid" => $d["noun"]);
    }
    $result = get_url($uu_url, $data); 
    $result = json_decode($result, true); 
    if ($result["code"] == "1") { 
        foreach ($result["data"] as $res) { 
            $yid = $res["id"]; 
            $kcname = $res["kcname"]; 
            $status = $res["status"]; 
            $process = $res["process"]; 
            $remarks = $res["remarks"]; 
            $kcks = $res["courseStartTime"]; 
            $kcjs = $res["courseEndTime"]; 
            $ksks = $res["examStartTime"]; 
            $ksjs = $res["examEndTime"];

    $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks
            ); 
        } 
    } else { 
        $b[] = array("code" => -1, "msg" => "查询失败!!!!"); 
    } 
    
    return $b;
}
</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder bsjk.php部分">else if ($type == "yqsl") {
	$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
	$dx_rl = $a["url"];
	$dx_url = "$dx_rl/api.php?act=budan";
	$result = get_url($dx_url, $data);
	$result = json_decode($result, true);
	return $result;
	
}</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder ztjk.php部分">else if ($type == "yqsl") {
$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
$uu_rl = $a["url"];
$uu_url = "$uu_rl/api.php?act=zt";
$result = get_url($uu_url, $data);
$result = json_decode($result, true);
return $result;}
	
}</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder xgjk.php部分">else if ($type == "yqsl") {
$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid, "xgmm" => $xgmm);
$uu_rl = $a["url"];
$uu_url = "$uu_rl/api.php?act=xgmm";
$result = get_url($uu_url, $data);
$result = json_decode($result, true);
return $result;}</pre>
<pre class="layui-code code-demo" lay-title="29 checkorder msjk.php部分">else if ($type == "yqsl") {
$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
$uu_rl = $a["url"];
$uu_url = "$uu_rl/api.php?act=ms";
$result = get_url($uu_url, $data);
$result = json_decode($result, true);
return $result;}</pre>
    </div>  

 

<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>


<script>
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function(){
      var element = layui.element;
      
      //…
    });
</script>

<script>
new Vue({
	el:"#loglist",
	data:{
		row:null
	},
	methods:{
		get:function(page){

		}
	},
	mounted(){
		this.get(1);
	}
});
</script>
<script>
layui.use(function(){
  // code
  layui.code({
    elem: '.code-demo',
    skin: 'dark',
    about: false,
    ln: false,
    header: true,
    preview: false,
    //tools: ['full', 'copy']
  });
})
//点击复制
/*function copyToClip(content, message = null) {
        var aux = document.createElement("input");
        aux.setAttribute("value", content);
        document.body.appendChild(aux);
        aux.select();
        document.execCommand("copy");
        document.body.removeChild(aux);
        if (message == null) {
            layer.msg("复制成功", {icon: 1});
        } else {
            layer.msg(message, {icon: 1});
        }
    }*/
</script>
<script>
             //禁止鼠标右击
      document.oncontextmenu = function() {
        event.returnValue = false;
      };
      //禁用开发者工具F12
      document.onkeydown = document.onkeyup = document.onkeypress = function(event) {
        let e = event || window.event || arguments.callee.caller.arguments[0];
        if (e && e.keyCode == 123) {
          e.returnValue = false;
          return false;
        }
      };
      let userAgent = navigator.userAgent;
      if (userAgent.indexOf("Firefox") > -1) {
        let checkStatus;
        let devtools = /./;
        devtools.toString = function() {
          checkStatus = "on";
        };
        setInterval(function() {
          checkStatus = "off";
          console.log(devtools);
          console.log(checkStatus);
          console.clear();
          if (checkStatus === "on") {
            let target = "";
            try {
              window.open("about:blank", (target = "_self"));
            } catch (err) {
              let a = document.createElement("button");
              a.onclick = function() {
                window.open("about:blank", (target = "_self"));
              };
              a.click();
            }
          }
        }, 200);
      } else {
        //禁用控制台
        let ConsoleManager = {
          onOpen: function() {
            alert("Console is opened");
          },
          onClose: function() {
            alert("Console is closed");
          },
          init: function() {
            let self = this;
            let x = document.createElement("div");
            let isOpening = false,
              isOpened = false;
            Object.defineProperty(x, "id", {
              get: function() {
                if (!isOpening) {
                  self.onOpen();
                  isOpening = true;
                }
                isOpened = true;
                return true;
              }
            });
            setInterval(function() {
              isOpened = false;
              console.info(x);
              console.clear();
              if (!isOpened && isOpening) {
                self.onClose();
                isOpening = false;
              }
            }, 200);
          }
        };
        ConsoleManager.onOpen = function() {
          //打开控制台，跳转
          let target = "";
          try {
            window.open("about:blank", (target = "_self"));
          } catch (err) {
            let a = document.createElement("button");
            a.onclick = function() {
              window.open("about:blank", (target = "_self"));
            };
            a.click();
          }
        };
        ConsoleManager.onClose = function() {
          alert("Console is closed!!!!!");
        };
        ConsoleManager.init();
      }
        </script>