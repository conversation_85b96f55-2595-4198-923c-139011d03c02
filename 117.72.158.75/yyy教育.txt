yyy教育（http://*************:6966/）
登陆账号：573749877
登陆密码：liuyaxin123

登陆api：
请求 URL
http://*************:6966/api/login
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-type
application/json; charset=utf-8
date
Mon, 11 Aug 2025 13:50:43 GMT
expires
Mon, 11 Aug 2025 13:50:42 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
content-length
49
content-type
application/json
host
*************:6966
origin
http://*************:6966
proxy-connection
keep-alive
referer
http://*************:6966/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest
负载：
{username: "573749877", password: "liuyaxin123"}
password
: 
"liuyaxin123"
username
: 
"573749877"

返回登陆数据：
{code: 200, data: {username: "573749877", nickname: "573749877", roles: ["common"],…}, message: "登录成功"}
code
: 
200
data
: 
{username: "573749877", nickname: "573749877", roles: ["common"],…}
accessToken
: 
"ea6c641da8db8e02c01063dcddaa8690"
expires
: 
"2030/10/30 00:00:00"
nickname
: 
"573749877"
permissions
: 
["permission:btn:add", "permission:btn:edit"]
0
: 
"permission:btn:add"
1
: 
"permission:btn:edit"
refreshToken
: 
"ea6c641da8db8e02c01063dcddaa8690"
roles
: 
["common"]
username
: 
"573749877"
message
: 
"登录成功"




本网站可学项目api：
请求 URL
http://*************:6966/api/site
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-type
application/json; charset=utf-8
date
Sun, 10 Aug 2025 13:23:10 GMT
expires
Sun, 10 Aug 2025 13:23:09 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer cb3523165cb6093cbc556e50b5d7b4c4
content-length
14
content-type
application/json
cookie
authorized-token={%22accessToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22%2C%22expires%22:1919520000000%2C%22refreshToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22}; multiple-tabs=true
host
*************:6966
origin
http://*************:6966
proxy-connection
keep-alive
referer
http://*************:6966/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest

负载：
{version: ""}
version
: 
""

会返回本网站所有可以学习的项目的名称，id，价格，说明，网址
{
    "code": 200,
    "data": {
        "version": "20250810211614",
        "lastoid": 841,
        "itemCount": 1365,
        "list": [
            {
                "id": 3501,
                "name": "全国高校教师网络培训中心-人工智能赋能高等教育人才培养",
                "trans": "视频 加速 学完全部项目 &进度有延迟，学完后请过段时间查看 格式：账号 密码",
                "url": "https:\/\/szh.enetedu.com\/site\/personalCenter\/MyCourse",
                "price_unit": "3 \/账号"
            },
            {
                "id": 3138,
                "name": "遂宁开放大学",
                "trans": "视频+考试 格式：账号 密码 培训名",
                "url": "https:\/\/snzyjs.peixunyun.cn\/#\/homepage",
                "price_unit": "3 \/培训"
            },
            {
                "id": 2195,
                "name": "教师专业发展平台",
                "trans": "（如有作业需求可提交到-单独作业专用）视频 格式：账号 密码 培训名",
                "url": "http:\/\/peixun.yanxiuonline.com\/",
                "price_unit": "2 \/培训"

以下省略