/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableEditable={exports:{}}.exports}})(this,function(){'use strict';function a(a,b){if(!(a instanceof b))throw new TypeError('Cannot call a class as a function')}function b(a,b){if(!a)throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called');return b&&('object'==typeof b||'function'==typeof b)?b:a}function c(a,b){if('function'!=typeof b&&null!==b)throw new TypeError('Super expression must either be null or a function, not '+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}var d=function(){function a(a,b){for(var c,d=0;d<b.length;d++)c=b[d],c.enumerable=c.enumerable||!1,c.configurable=!0,'value'in c&&(c.writable=!0),Object.defineProperty(a,c.key,c)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=function a(b,c,d){null===b&&(b=Function.prototype);var e=Object.getOwnPropertyDescriptor(b,c);if(e===void 0){var f=Object.getPrototypeOf(b);return null===f?void 0:a(f,c,d)}if('value'in e)return e.value;var g=e.get;return void 0===g?void 0:g.call(d)};(function(f){var g=f.fn.bootstrapTable.utils;f.extend(f.fn.bootstrapTable.defaults,{editable:!0,onEditableInit:function(){return!1},onEditableSave:function(){return!1},onEditableShown:function(){return!1},onEditableHidden:function(){return!1}}),f.extend(f.fn.bootstrapTable.Constructor.EVENTS,{"editable-init.bs.table":'onEditableInit',"editable-save.bs.table":'onEditableSave',"editable-shown.bs.table":'onEditableShown',"editable-hidden.bs.table":'onEditableHidden'}),f.BootstrapTable=function(h){function i(){return a(this,i),b(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return c(i,h),d(i,[{key:'initTable',value:function(){var a=this;e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'initTable',this).call(this),this.options.editable&&f.each(this.columns,function(b,c){if(c.editable){var d={},e=[],h='editable-',i=function(a,b){var c=a.replace(/([A-Z])/g,function(a){return'-'+a.toLowerCase()});0===c.indexOf(h)&&(d[c.replace(h,'data-')]=b)};f.each(a.options,i),c.formatter=c.formatter||function(a){return a},c._formatter=c._formatter?c._formatter:c.formatter,c.formatter=function(b,h,j){var k=g.calculateObjectValue(c,c._formatter,[b,h,j],b);f.each(c,i),f.each(d,function(a,b){e.push(' '+a+'="'+b+'"')});var l=!1;if(c.editable.hasOwnProperty('noeditFormatter')&&(l=c.editable.noeditFormatter(b,h,j))){var p=c.editable.noeditFormatter(b,h,j);if(!p.hasOwnProperty('class')){p.class=''}l='<a href="javascript:void(0)"\n              data-name="'+c.field+'"\n              data-pk="'+h[a.options.idField]+'"\n              class="'+p.class+'"              data-value="'+k+'"\n              '+e.join('')+'></a>'}return!1===l?'<a href="javascript:void(0)"\n              data-name="'+c.field+'"\n              data-pk="'+h[a.options.idField]+'"\n              data-value="'+k+'"\n              '+e.join('')+'></a>':l}}})}},{key:'initBody',value:function(a){var b=this;e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'initBody',this).call(this,a),this.options.editable&&(f.each(this.columns,function(a,c){if(c.editable){var d=b.getData(),e=b.$body.find('a[data-name="'+c.field+'"]');e.each(function(a,b){var e=f(b),h=e.closest('tr'),i=h.data('index'),j=d[i],k=g.calculateObjectValue(c,c.editable,[i,j,e],{});e.editable(k)}),e.off('save').on('save',function(a,d){var e=a.currentTarget,g=d.submitValue,h=f(e),i=b.getData(),j=h.parents('tr[data-index]').data('index'),k=i[j],l=k[c.field];h.data('value',g),k[c.field]=g,b.trigger('editable-save',c.field,k,l,h),b.resetFooter()}),e.off('shown').on('shown',function(a,d){var e=a.currentTarget,g=f(e),h=b.getData(),i=g.parents('tr[data-index]').data('index'),j=h[i];b.trigger('editable-shown',c.field,j,g,d)}),e.off('hidden').on('hidden',function(a,d){var e=a.currentTarget,g=f(e),h=b.getData(),i=g.parents('tr[data-index]').data('index'),j=h[i];b.trigger('editable-hidden',c.field,j,g,d)})}}),this.trigger('editable-init'))}}]),i}(f.BootstrapTable)})(jQuery)});