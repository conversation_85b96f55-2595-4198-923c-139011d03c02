<?php
$title = '信息核对';
require_once('head.php');  
?>
<style>
.not-exist-item {
    padding: 10px;
    margin-bottom: 8px;
    border-left: 3px solid #ff4d4f;
    background-color: #fff2f0;
    border-radius: 4px;
}

.account-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.text-danger {
    color: #ff4d4f;
    font-weight: bold;
}

.copy-btn {
    margin-left: 10px;
    padding: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
}

.copy-btn:hover {
    background-color: #f0f0f0;
}

.badge-danger {
    background-color: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    margin-left: 10px;
}
</style>
<div class="app-content-body">
    <div class="wrapper-md control" id="add" v-cloak>
        <!-- 顶部面板保持不变 -->
        <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 10px;">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">订单账号信息核对</div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
                <form class="form-horizontal devform">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">商品选择</label>
                        <div class="col-sm-9">
                            <el-select 
                                id="select" 
                                v-model="cid" 
                                @change="tips(cid)" 
                                filterable 
                                placeholder="直接输入账号信息查询或选择平台" 
                                style="background: url('../index/arrow.png')  no-repeat scroll 99%; width:100%">
                                <el-option 
                                    v-for="class2 in class1" 
                                    :key="class2.cid"    
                                    :label="class2.name  + '→' + class2.price  + '币'" 
                                    :value="class2.cid">   
                                    <span style="float: left">{{ class2.name  }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ class2.price  }}币</span>
                                </el-option>
                            </el-select>
                        </div>
                    </div>
 
                    <div class="form-group">
                        <label class="col-sm-2 control-label">账号信息</label>
                        <div class="col-sm-9">
                            <textarea 
                                v-model="userinfo" 
                                placeholder="请输入账号信息，每行账号 密码 或 学校 账号 密码" 
                                rows="15" 
                                class="form-control"></textarea>
                        </div>
                    </div>
 
                    <div class="col-sm-offset-2 col-sm-12">
                        <button type="button" @click="showExcelModal" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-warm">读取EXCEL</button>
                        <button type="button" @click="checkUserinfo" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-danger">检查数据</button>
                        <button type="button" @click="get" style="border-radius: 13px;" class="layui-btn layui-btn-sm">核对数据</button>
                        <button type="reset" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-primary">清空面板</button>
                    </div>
                </form>
            </div>
        </div>
 
        <!-- 添加的进度显示 -->
        <div v-if="rows.length > 0 && !showResults" style="text-align: center; margin-bottom: 10px;">
            正在核对: {{ rows.length }} / {{ stats.total }}
        </div>

        <!-- 查询结果面板 -->
        <div class="layui-col-md12" v-show="showResults">
            <div class="panel panel-default" style="border-radius: 10px;">
                <div class="panel-heading font-bold bg-white" style="border-radius: 10px;">
                    检测结果 (共{{ stats.total  }}条，存在{{ stats.exists  }}条，不存在{{ stats.notExists  }}条)
                </div>
                <div class="panel-body">
                    <!-- 一键复制按钮 -->
                    <button @click="copyAllNonExistent" style="border-radius: 13px; margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-danger">一键复制所有不存在的订单</button>
                    <!-- 显示不存在的订单 -->
                    <div v-for="(row, index) in filteredRows" :key="index" class="not-exist-item">
                        <div class="account-info">
                            <span>{{ row.userinfo  }}</span>
                            <span class="badge-danger">{{ row.status  }}</span>
                            <button @click="copyToClipboard(row.userinfo)" class="btn btn-xs btn-default copy-btn">
                                <i class="fa fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
var vm = new Vue({
    el: "#add",
    data: function() {
        return {
            rows: [], // 所有订单数据
            userinfo: '',
            cid: '',
            class1: '',
            showResults: false,
            stats: {
                total: 0,
                exists: 0,
                notExists: 0 
            }
        }
    },
    computed: {
        // 过滤只显示不存在的订单
        filteredRows: function() {
            return this.rows.filter(row => row.status === '不存在');
        }
    },
    methods: {
        get: async function() {
            if (!this.userinfo.trim()) {
                layer.msg("请输入账号信息");
                return;
            }
 
            const lines = this.userinfo.split('\n').filter(line => line.trim()); 
            this.stats.total = lines.length; 
            this.stats.exists = 0;
            this.stats.notExists = 0;
            this.rows = [];
            this.showResults = false;
 
            for (const [index, line] of lines.entries()) {
                
                try {
                    const formData = new FormData();
                    formData.append('cid', this.cid); 
                    formData.append('userinfo', line.trim()); 
 
                    const response = await axios.post("/apisub.php?act=orderusertest", formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    });
 
                    const result = response.data; 
                    
                    const rowData = {
                        userinfo: line.trim(), 
                        status: result.code === 1 ? '存在' : '不存在',
                        rawData: result.data || null
                    };
 
                    this.rows.push(rowData); 
                    
                    if (result.code === 1) {
                        this.stats.exists++; 
                    } else {
                        this.stats.notExists++; 
                    }
 
                } catch (error) {
                    console.error("请求失败:", error);
                    this.rows.push({ 
                        userinfo: line.trim(), 
                        status: '请求失败',
                        rawData: null
                    });
                } finally {
                    
                    if (index < lines.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
            }
            
            this.showResults = true;
 
            // 使用 layer.alert 显示检测结果
            layer.alert(`检测完成！共 ${this.stats.total} 条，存在 ${this.stats.exists} 条，不存在 ${this.stats.notExists} 条`, {
                title: '检测结果',
                icon: 1
            });
        },
        
        copyToClipboard: function(text) {
            const textarea = document.createElement('textarea'); 
            textarea.value = text;
            document.body.appendChild(textarea); 
            textarea.select(); 
            document.execCommand('copy'); 
            document.body.removeChild(textarea); 
            layer.msg('已复制到剪贴板', {icon: 1});
        },
 
        // 一键复制所有不存在的订单 
        copyAllNonExistent: function() {
            const nonExistentOrders = this.rows  
                .filter(row => row.status === '不存在')
                .map(row => row.userinfo) 
                .join('\n');
 
            if (nonExistentOrders) {
                this.copyToClipboard(nonExistentOrders); 
            } else {
                layer.msg('没有不存在的订单可以复制', {icon: 2});
            }
        },
 
        getclass: async function() {
            try {
                const load = layer.load(2);  
                const response = await this.$http.post("/apisub.php?act=getclassfl");  
                layer.close(load);  
 
                if (response.data.code === 1) {
                    this.class1 = response.body.data;  
                    this.filteredClass1 = this.class1;  
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error(error);  
            }
        },
 
        showExcelModal: function() {
            layer.open({ 
                type: 1,
                title: '上传Excel',
                content: 
                    `<input type="file" id="excelFile" accept=".xlsx,.xls" />` +
                    `<button type="button" id="readExcelBtn" onclick="vm.readExcel()">读取并解析文件</button>`+
                    `<br>文件上传后点读取并解析文件<br>所有账号信息会显示在上方的框中<br>读取时会默认丢弃第一行的数据（表头）<br>默认表格格式为 学校 账号 密码 课程`,
                area: ['300px', '200px']
            });
        },
 
        readExcel: function() {
            var file = document.getElementById('excelFile').files[0]; 
            if (!file) {
                layer.msg("请选择Excel文件");
                return;
            }
            var reader = new FileReader();
            reader.onload = function(e) {
                var data = e.target.result; 
                var workbook = XLSX.read(data, { type: 'binary' });
                var sheetName = workbook.SheetNames[0];
                var sheet = workbook.Sheets[sheetName];
                var json = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '' });
                var userData = json.map(row => row.join(' '));
                vm.userinfo = userData.join('\n'); 
                layer.msg("数据读取成功");
                layer.closeAll(); 
            };
            reader.readAsBinaryString(file); 
        },
 
        checkUserinfo: function() {
            var lines = this.userinfo.split('\n'); 
            var errors = [];
            var correctedLines = [];
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim(); 
                var correctedLine = line.replace(/ +/g, ' '); 
                correctedLines.push(correctedLine); 
                var parts = correctedLine.split(' ');
                if (parts.length < 2 || parts.length > 3) {
                    errors.push('第' + (i+1) + '行数据数量不正确，此行有' + parts.length + '个数据。');
                }
            }
            if (errors.length > 0) {
                layer.alert(errors.join('\n'), {title: '数据检查结果', icon: 5});
            } else {
                layer.msg('数据检查通过！', {icon: 1});
            }
            this.userinfo = correctedLines.join('\n'); 
        },
 
        tips: function(message) {
            console.log("Selected CID:", message);
        }
    },
    mounted() {
        this.getclass();  
    }
});
</script>