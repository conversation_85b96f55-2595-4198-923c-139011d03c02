<?php
/**
 * yyy教育对接测试脚本
 * 用于验证yyy教育货源对接是否正常工作
 */

// 引入必要的文件
require_once '../confing/config.php';
require_once '../Checkorder/configuration.php';

// 测试配置
$test_config = array(
    'hid' => 1, // 货源ID，需要根据实际情况调整
    'test_account' => 'test_user', // 测试账号
    'test_password' => 'test_pass', // 测试密码
    'test_course' => '测试课程名称',
    'lastoid' => '2864' // 测试用的lastoid
);

echo "<h1>yyy教育对接测试</h1>\n";
echo "<hr>\n";

// 测试1：登录功能
echo "<h2>测试1：登录功能</h2>\n";
$login_data = array(
    "username" => "*********", 
    "password" => "liuyaxin123"
);
$login_url = "http://*************:6966/api/login";
$headers = array(
    "Content-Type: application/json",
    "Accept: application/json, text/plain, */*",
    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
);

$login_result = get_url3($login_url, json_encode($login_data), "", $headers);
$login_response = json_decode($login_result, true);

if ($login_response && $login_response['code'] == 200) {
    echo "✅ 登录成功<br>\n";
    echo "用户名: " . $login_response['data']['username'] . "<br>\n";
    echo "Token: " . substr($login_response['data']['accessToken'], 0, 20) . "...<br>\n";
    $access_token = $login_response['data']['accessToken'];
    $refresh_token = $login_response['data']['refreshToken'];
} else {
    echo "❌ 登录失败: " . ($login_response['message'] ?? '未知错误') . "<br>\n";
    exit;
}

echo "<hr>\n";

// 测试2：获取网站列表
echo "<h2>测试2：获取网站列表</h2>\n";

// 构建Cookie
$token_data = array(
    "accessToken" => $access_token,
    "expires" => 1919520000000,
    "refreshToken" => $refresh_token
);
$token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
$encoded_token = urlencode($token_json);
$cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

$site_data = array("version" => "");
$site_headers = array(
    "Content-Type: application/json",
    "Accept: application/json, text/plain, */*",
    "Authorization: Bearer " . $access_token,
    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
);

$site_url = "http://*************:6966/api/site";
$site_result = get_url3($site_url, json_encode($site_data), $cookie, $site_headers);
$site_response = json_decode($site_result, true);

if ($site_response && $site_response['code'] == 200) {
    echo "✅ 获取网站列表成功<br>\n";
    echo "网站数量: " . $site_response['data']['itemCount'] . "<br>\n";
    echo "版本: " . $site_response['data']['version'] . "<br>\n";
    echo "最新订单ID: " . $site_response['data']['lastoid'] . "<br>\n";
    
    // 显示前5个网站
    if (isset($site_response['data']['list']) && count($site_response['data']['list']) > 0) {
        echo "<h3>前5个网站示例:</h3>\n";
        for ($i = 0; $i < min(5, count($site_response['data']['list'])); $i++) {
            $site = $site_response['data']['list'][$i];
            echo ($i + 1) . ". " . $site['name'] . " (ID: " . $site['id'] . ", 价格: " . $site['price_unit'] . ")<br>\n";
        }
    }
} else {
    echo "❌ 获取网站列表失败: " . ($site_response['message'] ?? '未知错误') . "<br>\n";
}

echo "<hr>\n";

// 测试3：查课功能
echo "<h2>测试3：查课功能</h2>\n";

$query_data = array(
    "lastoid" => $test_config['lastoid'],
    "orderData" => $test_config['test_account'] . "\t" . $test_config['test_password'],
    "orderNote" => "",
    "search" => "1"
);

$query_headers = array(
    "Content-Type: application/json",
    "Accept: application/json, text/plain, */*",
    "Authorization: Bearer " . $access_token,
    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
);

$query_url = "http://*************:6966/api/order";
$query_result = get_url3($query_url, json_encode($query_data), $cookie, $query_headers);
$query_response = json_decode($query_result, true);

if ($query_response && $query_response['code'] == 200) {
    echo "✅ 查课接口调用成功<br>\n";
    if (isset($query_response['data']) && is_array($query_response['data'])) {
        echo "找到课程数量: " . count($query_response['data']) . "<br>\n";
        foreach ($query_response['data'] as $index => $course) {
            echo ($index + 1) . ". " . $course . "<br>\n";
        }
    } else {
        echo "未找到课程数据<br>\n";
    }
} else {
    echo "⚠️ 查课接口调用结果: " . ($query_response['message'] ?? '未知错误') . "<br>\n";
    echo "这可能是因为测试账号不存在，属于正常情况<br>\n";
}

echo "<hr>\n";

// 测试4：订单查询功能
echo "<h2>测试4：订单查询功能</h2>\n";

$order_query_data = array(
    "lastoid" => "",
    "odname" => "",
    "nickname" => "",
    "notetype" => "1",
    "note" => "",
    "statusbox" => array(),
    "page" => 1,
    "pageSize" => 10
);

$order_query_result = get_url3("http://*************:6966/api/getorder", json_encode($order_query_data), $cookie, $query_headers);
$order_query_response = json_decode($order_query_result, true);

if ($order_query_response && $order_query_response['code'] == 200) {
    echo "✅ 订单查询接口调用成功<br>\n";
    if (isset($order_query_response['data']['records'])) {
        echo "订单总数: " . $order_query_response['data']['total'] . "<br>\n";
        echo "当前页订单数: " . count($order_query_response['data']['records']) . "<br>\n";
    }
} else {
    echo "❌ 订单查询失败: " . ($order_query_response['message'] ?? '未知错误') . "<br>\n";
}

echo "<hr>\n";

// 测试5：系统集成测试
echo "<h2>测试5：系统集成测试</h2>\n";

// 测试查课接口集成
echo "<h3>5.1 查课接口集成测试</h3>\n";
try {
    $result = getWk($test_config['hid'], $test_config['lastoid'], '测试学校', $test_config['test_account'], $test_config['test_password']);
    if ($result && isset($result['code'])) {
        if ($result['code'] == 1) {
            echo "✅ 系统查课接口集成成功<br>\n";
        } else {
            echo "⚠️ 系统查课接口返回: " . $result['msg'] . "<br>\n";
        }
    } else {
        echo "❌ 系统查课接口集成失败<br>\n";
    }
} catch (Exception $e) {
    echo "❌ 系统查课接口异常: " . $e->getMessage() . "<br>\n";
}

echo "<hr>\n";

// 总结
echo "<h2>测试总结</h2>\n";
echo "<p>✅ 表示测试通过</p>\n";
echo "<p>⚠️ 表示测试有警告但可能正常</p>\n";
echo "<p>❌ 表示测试失败</p>\n";
echo "<p><strong>注意：</strong>查课功能可能因为测试账号不存在而返回空结果，这是正常现象。</p>\n";
echo "<p><strong>建议：</strong>使用真实的学员账号密码进行完整测试。</p>\n";

?>
