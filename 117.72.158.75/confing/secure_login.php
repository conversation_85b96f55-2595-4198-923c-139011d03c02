<?php
/**
 * 安全登录验证函数
 * 支持新旧密码格式的兼容性验证
 */

if (!defined('IN_CRONLITE')) {
    die('Access denied');
}

/**
 * 验证密码
 * @param string $input_password 用户输入的密码
 * @param string $stored_password 数据库中存储的密码
 * @return bool
 */
function verify_password($input_password, $stored_password) {
    // 检查是否为新的哈希密码（以$开头）
    if (strpos($stored_password, '$') === 0) {
        // 使用PHP的password_verify验证
        return password_verify($input_password, $stored_password);
    } else {
        // 兼容旧的明文密码
        return $input_password === $stored_password;
    }
}

/**
 * 安全的密码哈希
 * @param string $password 明文密码
 * @return string 哈希后的密码
 */
function secure_password_hash($password) {
    return password_hash($password, PASSWORD_ARGON2ID);
}

/**
 * 检查密码强度
 * @param string $password 密码
 * @return array 返回检查结果
 */
function check_password_strength($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = '密码长度至少8位';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = '密码必须包含小写字母';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = '密码必须包含大写字母';
    }
    
    if (!preg_match('/\d/', $password)) {
        $errors[] = '密码必须包含数字';
    }
    
    if (!preg_match('/[@$!%*?&]/', $password)) {
        $errors[] = '密码必须包含特殊字符(@$!%*?&)';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'strength' => empty($errors) ? 'strong' : 'weak'
    ];
}

/**
 * 记录登录尝试
 * @param string $username 用户名
 * @param string $ip IP地址
 * @param bool $success 是否成功
 * @param string $reason 失败原因
 */
function log_login_attempt($username, $ip, $success, $reason = '') {
    global $DB;
    
    $sql = "INSERT INTO qingka_wangke_login_attempts (username, ip, user_agent, attempt_time, success, failure_reason) VALUES (?, ?, ?, NOW(), ?, ?)";
    $params = [
        $username,
        $ip,
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $success ? 1 : 0,
        $reason
    ];
    
    $DB->prepare_query($sql, $params);
}

/**
 * 检查登录尝试次数
 * @param string $ip IP地址
 * @param int $max_attempts 最大尝试次数
 * @param int $lockout_time 锁定时间（秒）
 * @return bool
 */
function check_login_attempts($ip, $max_attempts = 5, $lockout_time = 900) {
    global $DB;
    
    $sql = "SELECT COUNT(*) as attempts FROM qingka_wangke_login_attempts WHERE ip = ? AND success = 0 AND attempt_time > ?";
    $params = [$ip, date('Y-m-d H:i:s', time() - $lockout_time)];
    
    $result = $DB->prepare_getrow($sql, $params);
    
    return ($result['attempts'] ?? 0) < $max_attempts;
}

/**
 * 清除成功登录后的失败记录
 * @param string $ip IP地址
 */
function clear_failed_attempts($ip) {
    global $DB;
    
    $sql = "DELETE FROM qingka_wangke_login_attempts WHERE ip = ? AND success = 0";
    $DB->prepare_query($sql, [$ip]);
}

/**
 * 生成安全的随机字符串
 * @param int $length 长度
 * @return string
 */
function generate_secure_random($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * 验证二次验证码
 * @param string $input_code 输入的验证码
 * @return bool
 */
function verify_two_factor_code($input_code) {
    global $verification, $conf;
    
    // 优先使用配置表中的验证码
    $stored_code = $conf['verification_code'] ?? $verification ?? '';
    
    return !empty($stored_code) && $input_code === $stored_code;
}

/**
 * 安全的会话管理
 * @param array $user_data 用户数据
 * @return string 返回token
 */
function create_secure_session($user_data) {
    global $password_hash;
    
    // 生成更安全的会话ID
    $session_id = hash('sha256', $user_data['user'] . $user_data['pass'] . $password_hash . time());
    $token = authcode($user_data['user'] . "\t" . $session_id, "ENCODE", SYS_KEY);
    
    return $token;
}

/**
 * 更新用户密码为安全哈希
 * @param int $uid 用户ID
 * @param string $new_password 新密码
 */
function upgrade_user_password($uid, $new_password) {
    global $DB;
    
    $hashed_password = secure_password_hash($new_password);
    $sql = "UPDATE qingka_wangke_user SET pass = ? WHERE uid = ?";
    $DB->prepare_query($sql, [$hashed_password, $uid]);
}

?>
