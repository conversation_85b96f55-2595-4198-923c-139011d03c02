<?php
$mod='blank';
$title='账号统计';
require_once('head.php');
?>
<div class="app-content-body">
<div class="wrapper-md control">
<div class="panel panel-default" id="orderlist" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;" v-cloak>
<div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">账号统计</div>
<div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
<div class="form-horizontal devform">
    <div class="layui-row layui-col-space10" style="display: flex; align-items: center;">
        <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" style="flex: 1;">
            <el-select id="select" v-model="cx.cid" filterable placeholder="项目名称" style="background: url('../index/arrow.png') no-repeat scroll 99%;width:100%">
                <el-option label="项目名称" value=""></el-option>
                <?php
                    $a = $DB->query("select * from qingka_wangke_class where status!=2 ");
                    while ($row = $DB->fetch($a)) {
                        $shortName = mb_strlen($row['name']) > 25 ? mb_substr($row['name'], 0, 25) . '...' : $row['name'];
                        echo '<el-option label="' . $shortName . '" value="' . $row['cid'] . '"></el-option>';
                    }
                ?>
            </el-select>
        </div>
        <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" style="flex: 1;">
            <el-input v-model="cx.school" placeholder="请输入学校名称"></el-input>
        </div>
        <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" style="flex: 1;">
            <el-input v-model="cx.kcname" placeholder="请输入课程名称"></el-input>
        </div>
        <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" style="flex: 1;">
            <el-select id="select" v-model="cx.pagesize" filterable placeholder="请选择每页订单数量" style="background: url('../index/arrow.png') no-repeat scroll 99%;width:100%">
                <el-option label="25条/页" value="25"></el-option>
                <el-option label="50条/页" value="50"></el-option>
                <el-option label="100条/页" value="100"></el-option>
                <el-option label="200条/页" value="200"></el-option>
                <el-option label="500条/页" value="500"></el-option>
                <el-option label="1000条/页" value="1000"></el-option>
            </el-select>
        </div>
        <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" style="flex: 1;">
            <el-button type="primary" size="medium" @click="get(1)">查询</el-button>&nbsp;
            <el-button type="primary" size="medium" @click="showExportDialog()">导出</el-button>
        </div>
    </div>
</div>

<div class="layui-col-xs12">
        <table class="table table-striped" style="width: 100%; text-align: center; margin: 0 auto;">
            <thead>
                <tr style="text-align: center;">
                    <th style="text-align: center;">学校</th>
                    <th style="text-align: center;">账号</th>
                    <th style="text-align: center;">密码</th>
                    <th style="text-align: center;">科目数</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="res in row.data" style="text-align: center;">
                    <td style="text-align: center;">{{res.school}}</td>
                    <td style="text-align: center;">{{res.user}}</td>
                    <td style="text-align: center;">{{res.pass}}</td>
                    <td style="text-align: center;">{{res.count}}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<ul class="pagination no-border" v-if="row.last_page>1">
			         <li><span>共 {{ row.total_count }} 条</span></li>
			         <li><a @click="get(1)">首页</a></li>
			         <li><a @click="row.current_page>1?get(row.current_page-1):''">&laquo;</a></li>
		             <li  @click="get(row.current_page-3)" v-if="row.current_page-3>=1"><a>{{ row.current_page-3 }}</a></li>
					 <li  @click="get(row.current_page-2)" v-if="row.current_page-2>=1"><a>{{ row.current_page-2 }}</a></li>
					 <li  @click="get(row.current_page-1)" v-if="row.current_page-1>=1"><a>{{ row.current_page-1 }}</a></li>
					 <li :class="{'active':row.current_page==row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
					 <li  @click="get(row.current_page+1)" v-if="row.current_page+1<=row.last_page"><a>{{ row.current_page+1 }}</a></li>
					 <li  @click="get(row.current_page+2)" v-if="row.current_page+2<=row.last_page"><a>{{ row.current_page+2 }}</a></li>
					 <li  @click="get(row.current_page+3)" v-if="row.current_page+3<=row.last_page"><a>{{ row.current_page+3 }}</a></li>		       			     
			         <li><a @click="row.last_page>row.current_page?get(row.current_page+1):''">&raquo;</a></li>
			         <li><a @click="get(row.last_page)">尾页</a></li>
</ul>

<!-- Export Dialog -->
<div id="exportDialog" style="display: none; padding: 20px;">
    <div style="margin-bottom: 15px;">
        <label>导出范围：</label>
        <el-radio-group v-model="exportScope">
            <el-radio label="current">当前页面</el-radio>
            <el-radio label="all">所有页面（筛选结果）</el-radio>
        </el-radio-group>
    </div>
    <div style="margin-bottom: 15px;">
        <label>导出格式：</label>
        <el-radio-group v-model="exportFormat">
            <el-radio label="txt">TXT文本</el-radio>
            <el-radio label="excel">Excel文件</el-radio>
        </el-radio-group>
    </div>
    <div style="margin-bottom: 15px;">
        <label>内容格式：</label>
        <el-radio-group v-model="exportContent">
            <el-radio label="1">学校+账号+密码+科目数</el-radio>
            <el-radio label="2">学校+账号+密码</el-radio>
            <el-radio label="3">账号+密码</el-radio>
        </el-radio-group>
    </div>
    <div style="text-align: center; margin-top: 20px;">
        <el-button type="primary" @click="doExport">确认导出</el-button>
        <el-button @click="closeExportDialog">取消</el-button>
    </div>
</div>

</div>
</div>

</div>
</div>
</div>


<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
vm = new Vue({
    el: "#orderlist",
    data: {
        row: [],
        phone: '',
        list: '',
        sex: [],
        exportScope: 'current',
        exportFormat: 'txt',
        exportContent: '1',
        cx: {
            cid: '',
            school: '',
            kcname: '',
            pagesize: '25',
        }
    },
    methods: {
        get: function(page) {
            var load = layer.load(2);
            data = { cx: this.cx, page };
            this.$http.post("/apisub.php?act=userquery", data, { emulateJSON: true }).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.row = data.body;
                } else {
                    layer.msg(data.data.msg, { icon: 2 });
                }
            });
        },
        showExportDialog: function() {
            layer.open({
                type: 1,
                title: '导出选项',
                area: ['500px', '350px'],
                content: $('#exportDialog')
            });
        },
        closeExportDialog: function() {
            layer.closeAll();
        },
        doExport: function() {
            if (this.exportScope === 'current') {
                this.exportCurrentPage();
            } else {
                this.exportAllPages();
            }
        },
        exportCurrentPage: function() {
            let data = [];
            let headers = [];
            const filterCondition = `筛选方式：${this.cx.cid || '无'}项目${this.cx.school || '无'}学校${this.cx.kcname || '无'}课程`;

            if (this.exportContent === '1') {
                headers = ['学校', '账号', '密码', '科目数'];
                data.push([filterCondition, '', '', '']); // Filter condition as first row
                data.push(headers); // Headers as second row
                this.row.data.forEach(item => {
                    data.push([item.school, item.user, item.pass, item.count]);
                });
            } else if (this.exportContent === '2') {
                headers = ['学校', '账号', '密码'];
                data.push([filterCondition, '', '']); // Filter condition as first row
                data.push(headers); // Headers as second row
                this.row.data.forEach(item => {
                    data.push([item.school, item.user, item.pass]);
                });
            } else {
                headers = ['账号', '密码'];
                data.push([filterCondition, '']); // Filter condition as first row
                data.push(headers); // Headers as second row
                this.row.data.forEach(item => {
                    data.push([item.user, item.pass]);
                });
            }

            this.generateExportFile(headers, data);
        },
        exportAllPages: function() {
            var load = layer.load(2);
            let exportParams = JSON.parse(JSON.stringify(this.cx));
            exportParams.pagesize = 100000;

            let params = {
                cx: exportParams,
                exportAll: true,
                exportContent: this.exportContent
            };

            this.$http.post("/apisub.php?act=userquery", params, { emulateJSON: true }).then(function(response) {
                layer.close(load);
                if (response.data.code == 1) {
                    let headers = [];
                    let exportData = [];
                    const filterCondition = `数据筛选方式：项目${this.cx.cid || '无'}、学校${this.cx.school || '无'}、课程${this.cx.kcname || '无'}`;

                    if (this.exportContent === '1') {
                        headers = ['学校', '账号', '密码', '科目数'];
                        exportData.push([filterCondition, '', '', '']); // Filter condition as first row
                        exportData.push(headers); // Headers as second row
                        response.body.data.forEach(item => {
                            exportData.push([item.school, item.user, item.pass, item.count]);
                        });
                    } else if (this.exportContent === '2') {
                        headers = ['学校', '账号', '密码'];
                        exportData.push([filterCondition, '', '']); // Filter condition as first row
                        exportData.push(headers); // Headers as second row
                        response.body.data.forEach(item => {
                            exportData.push([item.school, item.user, item.pass]);
                        });
                    } else {
                        headers = ['账号', '密码'];
                        exportData.push([filterCondition, '']); // Filter condition as first row
                        exportData.push(headers); // Headers as second row
                        response.body.data.forEach(item => {
                            exportData.push([item.user, item.pass]);
                        });
                    }

                    this.generateExportFile(headers, exportData);
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            });
        },
        generateExportFile: function(headers, data) {
            if (this.exportFormat === 'txt') {
                this.exportAsTxt(headers, data);
            } else {
                this.exportAsExcel(headers, data);
            }
            layer.closeAll();
        },
        exportAsTxt: function(headers, data) {
            let content = '';
            
            data.forEach(row => {
                content += row.join('\t') + '\n';
            });
            
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '账号数据_' + new Date().toLocaleDateString() + '.txt';
            link.click();
            URL.revokeObjectURL(url);
        },
        exportAsExcel: function(headers, data) {
            const wb = XLSX.utils.book_new();
            const wsData = [...data]; // Data already includes filter condition and headers
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            XLSX.utils.book_append_sheet(wb, ws, '账号数据');
            XLSX.writeFile(wb, '账号数据_' + new Date().toLocaleDateString() + '.xlsx');
        }
    },
    mounted() {
        this.get(1);
    }
});
</script>