<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>yyy教育项目管理</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .sync-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .sync-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .sync-button:hover {
            background: #0056b3;
        }
        .sync-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status-box {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>yyy教育项目管理</h1>
            <p>从yyy教育API同步最新的项目列表到本地数据库</p>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ul>
                <li><strong>同步项目</strong>：从yyy教育API获取最新的1175+个网站项目</li>
                <li><strong>自动去重</strong>：已存在的项目会更新信息，新项目会添加到数据库</li>
                <li><strong>实时更新</strong>：同步后用户可以立即看到最新的项目列表</li>
                <li><strong>建议频率</strong>：每周同步一次以获取最新项目</li>
            </ul>
        </div>

        <div class="sync-section">
            <h3>🔄 项目同步</h3>
            <button id="syncBtn" class="sync-button" onclick="syncProjects()">
                开始同步项目
            </button>
            <button id="testBtn" class="sync-button" onclick="testConnection()" style="background: #28a745;">
                测试连接
            </button>
            
            <div id="statusBox" class="status-box"></div>
            
            <div id="statsGrid" class="stats-grid" style="display: none;">
                <div class="stat-card">
                    <div id="totalProjects" class="stat-number">0</div>
                    <div class="stat-label">总项目数</div>
                </div>
                <div class="stat-card">
                    <div id="newAdded" class="stat-number">0</div>
                    <div class="stat-label">新增项目</div>
                </div>
                <div class="stat-card">
                    <div id="updated" class="stat-number">0</div>
                    <div class="stat-label">更新项目</div>
                </div>
                <div class="stat-card">
                    <div id="errors" class="stat-number">0</div>
                    <div class="stat-label">错误数量</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusBox = document.getElementById('statusBox');
            statusBox.className = `status-box status-${type}`;
            statusBox.innerHTML = message;
            statusBox.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('statusBox').style.display = 'none';
        }

        function updateStats(data) {
            document.getElementById('totalProjects').textContent = data.total_projects || 0;
            document.getElementById('newAdded').textContent = data.new_added || 0;
            document.getElementById('updated').textContent = data.updated || 0;
            document.getElementById('errors').textContent = data.errors || 0;
            document.getElementById('statsGrid').style.display = 'grid';
        }

        function testConnection() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.innerHTML = '<span class="loading"></span>测试中...';
            
            showStatus('正在测试yyy教育API连接...', 'info');
            
            fetch('sync_yyy_projects.php?test=1')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        showStatus('✅ 连接测试成功！API可以正常访问。', 'success');
                    } else {
                        showStatus('❌ 连接测试失败：' + data.msg, 'error');
                    }
                })
                .catch(error => {
                    showStatus('❌ 连接测试失败：' + error.message, 'error');
                })
                .finally(() => {
                    testBtn.disabled = false;
                    testBtn.innerHTML = '测试连接';
                });
        }

        function syncProjects() {
            const syncBtn = document.getElementById('syncBtn');
            syncBtn.disabled = true;
            syncBtn.innerHTML = '<span class="loading"></span>同步中...';
            
            showStatus('正在从yyy教育API同步项目列表，请稍候...', 'info');
            
            fetch('sync_yyy_projects.php')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        showStatus(`✅ 同步完成！<br>
                            总项目数：${data.data.total_projects}<br>
                            新增项目：${data.data.new_added}<br>
                            更新项目：${data.data.updated}<br>
                            错误数量：${data.data.errors}<br>
                            版本：${data.data.version}`, 'success');
                        updateStats(data.data);
                    } else {
                        showStatus('❌ 同步失败：' + data.msg, 'error');
                    }
                })
                .catch(error => {
                    showStatus('❌ 同步失败：' + error.message, 'error');
                })
                .finally(() => {
                    syncBtn.disabled = false;
                    syncBtn.innerHTML = '开始同步项目';
                });
        }

        // 页面加载时显示提示
        window.onload = function() {
            showStatus('👋 欢迎使用yyy教育项目管理工具！点击"测试连接"验证API可用性，然后点击"开始同步项目"获取最新项目列表。', 'info');
        };
    </script>
</body>
</html>
