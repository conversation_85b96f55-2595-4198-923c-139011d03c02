-- yyy教育货源对接SQL脚本
-- 创建时间: 2025-08-20

-- 1. 创建货源表（如果不存在）
CREATE TABLE IF NOT EXISTS `qingka_wangke_huoyuan` (
  `hid` int(11) NOT NULL AUTO_INCREMENT COMMENT '货源ID',
  `pt` varchar(50) NOT NULL COMMENT '平台类型标识',
  `name` varchar(100) NOT NULL COMMENT '货源名称',
  `url` varchar(255) NOT NULL COMMENT '货源API地址',
  `user` varchar(100) NOT NULL COMMENT '货源账号',
  `pass` varchar(255) NOT NULL COMMENT '货源密码',
  `token` varchar(255) DEFAULT NULL COMMENT 'API Token',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `cookie` text COMMENT 'Cookie信息',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `endtime` datetime DEFAULT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`hid`),
  KEY `idx_pt` (`pt`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货源管理表';

-- 2. 插入yyy教育货源配置
INSERT INTO `qingka_wangke_huoyuan` (
  `pt`, `name`, `url`, `user`, `pass`, `token`, `ip`, `cookie`, `status`, `addtime`
) VALUES (
  'yyy',
  'yyy教育 (1175+网站)',
  'http://*************:6966',
  '573749877',
  'liuyaxin123',
  '',
  '*************',
  '',
  1,
  NOW()
);

-- 3. 创建分类表（如果不存在）
CREATE TABLE IF NOT EXISTS `qingka_wangke_fenlei` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `text` text COMMENT '分类介绍',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类管理表';

-- 4. 插入yyy教育分类
INSERT IGNORE INTO `qingka_wangke_fenlei` (`name`, `text`, `sort`, `status`) VALUES
('yyy教育', 'yyy教育平台聚合1175+个教育网站，提供各类网课培训项目，包括教师培训、职业技能培训等。支持查课和下单功能。', 100, 1);

-- 5. 创建商品表（如果不存在）
CREATE TABLE IF NOT EXISTS `qingka_wangke_class` (
  `cid` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `getnoun` varchar(100) DEFAULT NULL COMMENT '获取标识',
  `noun` varchar(100) DEFAULT NULL COMMENT '平台标识',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `queryplat` int(11) DEFAULT NULL COMMENT '查询平台ID',
  `docking` int(11) DEFAULT NULL COMMENT '对接货源ID',
  `yunsuan` varchar(10) DEFAULT '*' COMMENT '运算符',
  `content` text COMMENT '商品介绍',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
  `fenlei` int(11) DEFAULT NULL COMMENT '分类ID',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`cid`),
  KEY `idx_fenlei` (`fenlei`),
  KEY `idx_status` (`status`),
  KEY `idx_docking` (`docking`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品管理表';

-- 6. 获取刚插入的货源ID和分类ID
SET @huoyuan_id = (SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'yyy' ORDER BY hid DESC LIMIT 1);
SET @fenlei_id = (SELECT id FROM qingka_wangke_fenlei WHERE name = 'yyy教育' ORDER BY id DESC LIMIT 1);

-- 7. 插入示例商品（基于yyy教育API返回的项目）
INSERT IGNORE INTO `qingka_wangke_class` (
  `sort`, `name`, `getnoun`, `noun`, `price`, `queryplat`, `docking`,
  `yunsuan`, `content`, `status`, `fenlei`
) VALUES
(100, 'yyy教育-合肥永君专业技术人员继续教育在线', '1683', '1683', 2.40, @huoyuan_id, @huoyuan_id, '*', '视频+考试 格式：账号 密码 培训名 (yyy教育聚合平台)', 1, @fenlei_id),
(99, 'yyy教育-通用查课下单', '2864', '2864', 2.00, @huoyuan_id, @huoyuan_id, '*', '支持1175+个教育网站 格式：账号 密码 课程名称 (使用lastoid参数)', 1, @fenlei_id),
(98, 'yyy教育-教师培训专用', '2000', '2000', 2.50, @huoyuan_id, @huoyuan_id, '*', '教师专业发展培训 格式：账号 密码 培训名称', 1, @fenlei_id);

-- 8. 创建订单表（如果不存在）
CREATE TABLE IF NOT EXISTS `qingka_wangke_order` (
  `oid` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `cid` int(11) NOT NULL COMMENT '商品ID',
  `hid` int(11) DEFAULT NULL COMMENT '货源ID',
  `ptname` varchar(255) DEFAULT NULL COMMENT '平台名称',
  `school` varchar(255) DEFAULT NULL COMMENT '学校',
  `name` varchar(100) DEFAULT NULL COMMENT '姓名',
  `user` varchar(100) NOT NULL COMMENT '账号',
  `pass` varchar(255) NOT NULL COMMENT '密码',
  `kcid` varchar(100) DEFAULT NULL COMMENT '课程ID',
  `kcname` varchar(255) DEFAULT NULL COMMENT '课程名称',
  `courseEndTime` varchar(50) DEFAULT NULL COMMENT '课程结束时间',
  `fees` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '费用',
  `noun` varchar(100) DEFAULT NULL COMMENT '平台标识',
  `miaoshua` tinyint(1) DEFAULT 0 COMMENT '是否秒刷',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `dockstatus` varchar(10) DEFAULT '0' COMMENT '对接状态',
  `status` varchar(50) DEFAULT '待处理' COMMENT '订单状态',
  `yid` varchar(100) DEFAULT NULL COMMENT '上游订单ID',
  `process` varchar(255) DEFAULT NULL COMMENT '进度',
  `remarks` text COMMENT '备注',
  `bsnum` int(11) DEFAULT 0 COMMENT '补刷次数',
  PRIMARY KEY (`oid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_cid` (`cid`),
  KEY `idx_hid` (`hid`),
  KEY `idx_status` (`status`),
  KEY `idx_addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单管理表';

-- 9. 显示配置完成信息
SELECT 
  '货源配置完成' as message,
  hid as 货源ID,
  name as 货源名称,
  url as API地址,
  user as 账号
FROM qingka_wangke_huoyuan 
WHERE pt = 'yyy';

SELECT 
  '分类配置完成' as message,
  id as 分类ID,
  name as 分类名称
FROM qingka_wangke_fenlei 
WHERE name = 'yyy教育';

SELECT 
  '商品配置完成' as message,
  COUNT(*) as 商品数量
FROM qingka_wangke_class 
WHERE fenlei = @fenlei_id;
