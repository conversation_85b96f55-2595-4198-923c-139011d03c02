<?php
include('../confing/common.php');
@header("Content-Type: application/json; charset=UTF-8");
date_default_timezone_set('Asia/Shanghai'); // 显式使用北京时间
if ($islogin != 1) {
    exit(json_encode(['code' => -1, 'msg' => '登录过期请重新登录', 'redirect' => '/index/login']));
}
$php_Self = substr($_SERVER['PHP_SELF'],strripos($_SERVER['PHP_SELF'],"/")+1);
    if($php_Self!="jkjg.php"){
        $msg = '%E6%96%87%E4%BB%B6%E9%94%99%E8%AF%AF';
        $msg = urldecode($msg);
       exit(json_encode(['code' => -1, 'msg' => $msg]));}
    if($userrow['uid'] != 1){
        $msg = '%E6%96%87%E4%BB%B6%E9%94%99%E8%AF%AF';
        $msg = urldecode($msg);
       exit(json_encode(['code' => -1, 'msg' => $msg]));}
// -------- 小工具 --------
function j($arr, $exit = true) {
    // 统一 JSON 输出（UTF-8 兜底，不转义中文）
    $json = json_encode($arr, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
    if ($exit) {
        exit($json);
    }
    return $json;
}
function db_escape_local($DB, $str) {
    if ($str === null) return '';
    if (is_array($str) || is_object($str)) $str = json_encode($str, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
    if (method_exists($DB, 'escape')) return $DB->escape($str);
    if (isset($DB->link)) return mysqli_real_escape_string($DB->link, $str);
    return addslashes($str);
}
// 读取 JSON/表单 POST 的兼容
$raw_body  = file_get_contents('php://input');
$post_data = json_decode($raw_body, true);
if (!is_array($post_data) || !count($post_data)) {
    // 兼容 application/x-www-form-urlencoded
    $post_data = $_POST;
}

$act = isset($_GET["act"]) ? daddslashes($_GET["act"]) : null;


// 登录校验
$noLoginActions = ['login', 'logout', 'register', 'test_showdoc_push'];
if (!in_array($act, $noLoginActions) && $islogin != 1) {
    j(['code' => -10, 'msg' => '请先登录']);
}

// 1. 获取所有货源和其所属分类
if ($act == 'get_huoyuan_with_fenlei') {
    $huoyuans = [];
    $rs = $DB->query("SELECT * FROM qingka_wangke_huoyuan ORDER BY hid ASC");
    while ($row = $DB->fetch($rs)) $huoyuans[$row['hid']] = $row;

    $classMap = [];
    $rs = $DB->query("SELECT c.docking, c.fenlei, f.name as fenlei_name 
                      FROM qingka_wangke_class c 
                      LEFT JOIN qingka_wangke_fenlei f ON c.fenlei = f.id
                      GROUP BY c.docking, c.fenlei");
    while ($row = $DB->fetch($rs)) {
        $hid = $row['docking'];
        $fid = $row['fenlei'];
        $classMap[$hid][$fid] = [
            'fenlei_id' => $fid,
            'fenlei_name' => $row['fenlei_name'] ? $row['fenlei_name'] : '未分类'
        ];
    }

    $config = $DB->get_row("SELECT * FROM qingka_wangke_class_config ORDER BY id DESC LIMIT 1");
    $huoyuan_price_rate    = $config && $config['huoyuan_price_rate']    ? json_decode($config['huoyuan_price_rate'], true)    : [];
    $huoyuan_category_price= $config && $config['huoyuan_category_price']? json_decode($config['huoyuan_category_price'], true): [];
    $skip_rule             = $config && $config['skip_rule']             ? json_decode($config['skip_rule'], true)             : [];
    $replace_rule          = $config && $config['replace_rule']          ? json_decode($config['replace_rule'], true)          : [];
    $klsjkg = $config && isset($config['klsjkg']) ? intval($config['klsjkg']) : 0;
    $qzkg  = $config && isset($config['qzkg'])   ? intval($config['qzkg'])   : 1;
    $tgkg  = $config && isset($config['tgkg'])   ? intval($config['tgkg'])   : 1;

    $hyid_arr = [];
    if ($config && $config['hyid']) $hyid_arr = explode(',', $config['hyid']);

    foreach ($huoyuans as $hid => &$h) {
        $farr = [];
        if (!empty($classMap[$hid])) {
            foreach ($classMap[$hid] as $fid => $data) {
                $farr[] = [
                    'fenlei_id'   => $data['fenlei_id'],
                    'fenlei_name' => $data['fenlei_name']
                ];
            }
        }
        $h['fenlei_list']    = $farr;
        $h['huoyuan_listen'] = in_array($hid, $hyid_arr) ? 1 : 0;
        $h['price_rate']     = isset($huoyuan_price_rate[$hid]) ? $huoyuan_price_rate[$hid] : 5.00;
        $h['category_price'] = isset($huoyuan_category_price[$hid]) ? $huoyuan_category_price[$hid] : [];
        $h['skip_rule']      = isset($skip_rule[$hid]) ? $skip_rule[$hid] : ['cidList'=>[],'cidRange'=>[],'fenleiList'=>[]];
        $h['replace_rule']   = isset($replace_rule[$hid]) ? $replace_rule[$hid] : [];
    }
    unset($h);

    j([
        'code' => 1,
        'data' => array_values($huoyuans),
        'klsjkg' => $klsjkg,
        'qzkg'   => $qzkg,
        'tgkg'   => $tgkg
    ]);
}

// 2. 拉全局配置
if ($act == 'get_config') {
    $row = $DB->get_row("SELECT * FROM qingka_wangke_class_config ORDER BY id DESC LIMIT 1");
    if ($row) {
        unset($row['id'], $row['created_at'], $row['updated_at'], $row['remark']);
        j(['code'=>1, 'data'=>$row]);
    } else {
        j(['code'=>0, 'msg'=>'暂无配置']);
    }
}

// 3. 保存配置（支持huoyuan_category_price）
if ($act == 'save_config') {
    $fields = [
        'hyid', 'huoyuan_price_rate', 'huoyuan_category_price',
        'skip_rule', 'replace_rule',
        'klsjkg', 'qzkg', 'tgkg',
        'tbms', 'ltxgjg', 'tskg', 'sdurl', 'zmsjkg',
        'tbjgkg', 'tbsmkg', 'tbsjmc', 'tbztkg', 'qzdqjg'
    ];
    $vals = [];
    foreach($fields as $f){
        $vals[$f] = isset($post_data[$f]) ? db_escape_local($DB, $post_data[$f]) : '';
    }

    if (empty($vals['hyid']) && isset($post_data['huoyuan_listen'])) {
        $huoyuan_listen_arr = is_array($post_data['huoyuan_listen']) ? $post_data['huoyuan_listen'] : json_decode($post_data['huoyuan_listen'], true);
        $idArr = [];
        if (is_array($huoyuan_listen_arr)) {
            foreach ($huoyuan_listen_arr as $hid=>$on) if($on) $idArr[] = $hid;
        }
        $vals['hyid'] = implode(',', $idArr);
    }

    $DB->query("DELETE FROM qingka_wangke_class_config WHERE 1=1");
    $sql = "INSERT INTO qingka_wangke_class_config 
        (hyid,huoyuan_price_rate,huoyuan_category_price,skip_rule,replace_rule,klsjkg,qzkg,tgkg,
        tbms,ltxgjg,tskg,sdurl,zmsjkg,
        tbjgkg,tbsmkg,tbsjmc,tbztkg,qzdqjg,remark)
        VALUES (
            '{$vals['hyid']}','{$vals['huoyuan_price_rate']}','{$vals['huoyuan_category_price']}','{$vals['skip_rule']}','{$vals['replace_rule']}','{$vals['klsjkg']}','{$vals['qzkg']}','{$vals['tgkg']}',
            '{$vals['tbms']}','{$vals['ltxgjg']}','{$vals['tskg']}','{$vals['sdurl']}','{$vals['zmsjkg']}',
            '{$vals['tbjgkg']}','{$vals['tbsmkg']}','{$vals['tbsjmc']}','{$vals['tbztkg']}','{$vals['qzdqjg']}',
            ''
        )";
    if($DB->query($sql)){
        j(['code'=>1,'msg'=>'保存成功']);
    }else{
        j(['code'=>-1,'msg'=>'保存失败: '.mysqli_error($DB->link)]);
    }
}

// 4. 一键清空所有配置
if ($act == 'clear_all_config') {
    $ret = $DB->query("DELETE FROM qingka_wangke_class_config WHERE 1=1");
    if ($ret) j(['code'=>1,'msg'=>'已清空所有配置']);
    j(['code'=>-1,'msg'=>'清空失败: '.mysqli_error($DB->link)]);
}

// 5. 获取最近日志（修复：分页 + UTF-8 兜底 + 可搜索）
if ($act == 'get_logs') {
    @set_time_limit(20);
    // 防止极端环境 memory_limit 太小
    $mem = ini_get('memory_limit');
    if ($mem && preg_match('/^(\d+)([KMG])?$/i', $mem, $m)) {
        $mult = ['K'=>1024,'M'=>1048576,'G'=>1073741824];
        $limit = (int)$m[1] * (isset($mult[strtoupper($m[2]??'M')]) ? $mult[strtoupper($m[2]??'M')] : 1048576);
        if ($limit < 256*1024*1024) @ini_set('memory_limit','256M');
    }

    // 允许搜索的字段（前端表头一致）
    $allowFields = ['huoyuan_name','fenlei_name','platform_name','action','data_before','data_after','operator','remark'];

    // 参数（不传也可用）
    $page     = max(1, (int)($_GET['page'] ?? 1));
    $pageSize = (int)($_GET['page_size'] ?? 5000);        // 默认给 500
    $pageSize = max(1, min(5000, $pageSize));            // 上限 2000
    $field    = trim((string)($_GET['field'] ?? ''));
    $kw       = trim((string)($_GET['kw'] ?? ''));
    $offset   = ($page-1) * $pageSize;

    // WHERE
    $where = '1';
    if ($kw !== '') {
        $safeKw = db_escape_local($DB, "%{$kw}%");
        if ($field !== '' && in_array($field, $allowFields, true)) {
            $where = "`{$field}` LIKE '{$safeKw}'";
        } else {
            $ors = [];
            foreach ($allowFields as $f) { $ors[] = "`{$f}` LIKE '{$safeKw}'"; }
            $where = '(' . implode(' OR ', $ors) . ')';
        }
    }

    // 总数
    $cntRow = $DB->get_row("SELECT COUNT(*) AS c FROM qingka_wangke_class_log WHERE {$where}");
    $total = (int)($cntRow['c'] ?? 0);

    // 查询（按 id DESC）
    $sql = "SELECT id,product_id,huoyuan_id,huoyuan_name,platform_name,fenlei_id,fenlei_name,
                   action,data_before,data_after,operator,op_time,remark,push_status
            FROM qingka_wangke_class_log
            WHERE {$where}
            ORDER BY id DESC
            LIMIT {$offset}, {$pageSize}";
    $rs = $DB->query($sql);

    $list = [];
    while ($r = $DB->fetch($rs)) {
        // UTF-8 兜底，避免 json_encode 报错
        foreach (['huoyuan_name','fenlei_name','platform_name','action','data_before','data_after','operator','op_time','remark'] as $k) {
            if (isset($r[$k]) && !mb_check_encoding($r[$k], 'UTF-8')) {
                $r[$k] = mb_convert_encoding($r[$k], 'UTF-8', 'UTF-8,GBK,GB2312,ISO-8859-1');
            }
        }
        $list[] = $r;
    }

    j(['code'=>1,'data'=>$list,'total'=>$total,'page'=>$page,'page_size'=>$pageSize]);
}

// 7. 执行本地前缀批量替换接口（保留原逻辑，补充北京时间与转义）
if ($act == 'execute_prefix_replace') {
    $config = $DB->get_row("SELECT * FROM qingka_wangke_class_config ORDER BY id DESC LIMIT 1");
    if (!$config) j(['code'=>-1, 'msg'=>'无全局配置']);

    $hyid_str = trim($config['hyid'] ?? '');
    $hyids = array_filter(array_map('intval', explode(',', $hyid_str)));
    if (!$hyids) j(['code'=>-2, 'msg'=>'未配置监听货源']);

    $replaceRule = $config['replace_rule'] ? json_decode($config['replace_rule'], true) : [];
    $skipRule    = $config['skip_rule']    ? json_decode($config['skip_rule'], true) : [];
    $qzkg        = isset($config['qzkg']) ? intval($config['qzkg']) : 1;

    $allCount = 0; $succCount = 0; $failCount = 0; $logs = [];

    // 获取分类名
    $getFenleiNameLocal = function($DB, $fenleiId) {
        if (!$fenleiId) return '';
        $row = $DB->get_row("SELECT name FROM qingka_wangke_fenlei WHERE id='{$fenleiId}' LIMIT 1");
        return $row ? $row['name'] : '';
    };
    // 获取货源名
    $getHuoyuanNameLocal = function($DB, $hid) {
        $row = $DB->get_row("SELECT name FROM qingka_wangke_huoyuan WHERE hid='{$hid}' LIMIT 1");
        return $row ? $row['name'] : "HID:{$hid}";
    };
    // 写日志
    $writePrefixLog = function($DB, $row, $before, $after, $pushStatus=3) use ($getFenleiNameLocal,$getHuoyuanNameLocal) {
        $nowTime = date('Y-m-d H:i:s');
        $hid = $row['docking'];
        $fenleiId = $row['fenlei'];
        $fenleiName = $getFenleiNameLocal($DB, $fenleiId);
        $huoyuanName = $getHuoyuanNameLocal($DB, $hid);
        $platformName = $row['name'];
        $remark = "类型：替换前缀 / hid={$hid} / 货源【{$huoyuanName}】 / 分类【{$fenleiName}】 / 商品CID【{$row['cid']}】 / 旧：【{$before}】 新：【{$after}】 / 时间：{$nowTime}";
        $sql = "INSERT INTO qingka_wangke_class_log
            (product_id, huoyuan_id, huoyuan_name, platform_name, fenlei_id, fenlei_name, action, data_before, data_after, operator, op_time, remark, push_status)
            VALUES (
                '{$row['cid']}', '{$hid}', '".db_escape_local($DB,$huoyuanName)."',
                '".db_escape_local($DB,$platformName)."', '{$fenleiId}', '".db_escape_local($DB,$fenleiName)."',
                '替换前缀', '".db_escape_local($DB,$before)."', '".db_escape_local($DB,$after)."',
                '手动前缀替换', '{$nowTime}', '".db_escape_local($DB,$remark)."', '{$pushStatus}'
            )";
        $DB->query($sql);
    };

    foreach ($hyids as $hid) {
        $repArr = isset($replaceRule[$hid]) ? $replaceRule[$hid] : [];
        if (!$repArr || !is_array($repArr) || !$qzkg) continue;

        $skip = $skipRule[$hid] ?? ['cidList'=>[], 'cidRange'=>[], 'fenleiList'=>[]];
        $skipCidList = isset($skip['cidList']) ? $skip['cidList'] : [];
        $skipCidRange= (isset($skip['cidRange']) && count($skip['cidRange'])==2) ? $skip['cidRange'] : [];
        $skipFenlei  = isset($skip['fenleiList']) ? $skip['fenleiList'] : [];

        $sql = "SELECT * FROM qingka_wangke_class WHERE docking='{$hid}'";
        $res = $DB->query($sql);
        while ($row = $DB->fetch($res)) {
            $noun = strval($row['noun']);
            $fenlei = $row['fenlei'];
            $skiped = false;
            if (!empty($skipCidList) && in_array($noun, $skipCidList)) $skiped = true;
            if (!$skiped && !empty($skipCidRange) && $noun >= $skipCidRange[0] && $noun <= $skipCidRange[1]) $skiped = true;
            if (!$skiped && !empty($skipFenlei) && in_array($fenlei, $skipFenlei)) $skiped = true;
            if ($skiped) continue;

            $oldName = $row['name'];
            $newName = $oldName;
            foreach ($repArr as $rule) {
                if (!empty($rule['from'])) {
                    $newName = preg_replace('/^' . preg_quote($rule['from'], '/') . '/u', $rule['to'], $newName);
                }
            }
            if ($oldName !== $newName) {
                $allCount++;
                $ret = $DB->query("UPDATE qingka_wangke_class SET name='".db_escape_local($DB,$newName)."' WHERE cid='{$row['cid']}'");
                if ($ret) $succCount++; else $failCount++;
                $writePrefixLog($DB, $row, $oldName, $newName, 3);
                $logs[] = [
                    'cid'=>$row['cid'],
                    'old'=>$oldName,
                    'new'=>$newName,
                    'fenlei'=>$getFenleiNameLocal($DB, $row['fenlei']),
                    'huoyuan'=>$getHuoyuanNameLocal($DB, $hid)
                ];
            }
        }
    }
    j([
        'code'=>1,
        'msg'=>"前缀批量替换已完成，成功：{$succCount} 条，失败：{$failCount} 条",
        'all'=>$allCount,'succ'=>$succCount,'fail'=>$failCount,'logs'=>$logs
    ]);
}

// 8. 未知操作
j(['code'=>-1,'msg'=>'未知操作']);
