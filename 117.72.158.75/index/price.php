<?php
$title = '商品列表';
require_once('head.php');
?>
<div class="app-content-body">
    <div class="wrapper-md control">
        <div class="row">
            <div class="col-sm-12">
                <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                    <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                        商品列表
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xs-6 col-sm-3">
                                <input type="text" id="searchkeyword" class="form-control" placeholder="输入关键词进行筛选">
                            </div>
                            <div class="col-xs-6 col-sm-2">
                                <!-- Element UI searchable select component -->
                                <div id="app">
                                    <el-select v-model="selectedRate" filterable placeholder="选择费率" @change="updatePrices">
                                        <el-option
                                            v-for="item in feeRates"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="col-xs-6 col-sm-2">
                                <button id="daochubtn" class="btn btn-info">导出当前价格表</button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table" id="priceTable">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 90px;">所在分类</th>
                                        <th scope="col">课程名称</th>
                                        <th scope="col">价格</th>
                                        <th scope="col">课程说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $a = $DB->query("SELECT * FROM qingka_wangke_class WHERE status=1 ORDER BY fenlei ASC");
                                    while ($rs = $DB->fetch($a)) {
                                        // 查询分类名称
                                        $fenlei_query = $DB->query("SELECT name FROM qingka_wangke_fenlei WHERE id = " . (int)$rs['fenlei']);
                                        $fenlei_row = $DB->fetch($fenlei_query);
                                        $fenlei_name = $fenlei_row ? $fenlei_row['name'] : '未知分类'; // 如果未找到分类，显示“未知分类”
                                        echo "<tr>
                                            <td>" . htmlspecialchars($fenlei_name) . "</td>
                                            <td>" . htmlspecialchars($rs['name']) . "</td>
                                            <td class='price' data-base-price='" . $rs['price'] . "'>" . ($rs['price'] * $userrow['addprice']) . "</td>
                                            <td>" . htmlspecialchars($rs['content']) . "</td>
                                        </tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
require_once("lightyearfooter.php");
require_once("footer.php");
?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    // Search functionality
    $("#searchkeyword").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $("#priceTable tbody tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Export table to Excel
    $('#daochubtn').click(function() {
        var keyword = $("#searchkeyword").val();
        var fileName = keyword + "项目价格表.xlsx";
        var wb = XLSX.utils.table_to_book(document.getElementById('priceTable'), {sheet:"商品价格表"});
        XLSX.writeFile(wb, fileName);
    });
});

// Vue.js app for Element UI select
new Vue({
    el: '#app',
    data: {
        selectedRate: <?php echo $userrow['addprice']; ?>,
        feeRates: [
            { value: <?php echo $userrow['addprice']; ?>, label: '当前费率 (<?php echo $userrow['addprice']; ?>)' },
            { value: 0.2, label: '0.2' },
            { value: 0.25, label: '0.25' },
            { value: 0.3, label: '0.3' },
            { value: 0.35, label: '0.35' },
            { value: 0.4, label: '0.4' },
            { value: 0.45, label: '0.45' },
            { value: 0.5, label: '0.5' },
            { value: 0.55, label: '0.55' },
            { value: 0.6, label: '0.6' },
            { value: 0.65, label: '0.65' },
            { value: 0.7, label: '0.7' },
            { value: 0.75, label: '0.75' },
            { value: 0.8, label: '0.8' }
        ]
    },
    methods: {
        updatePrices() {
            var rate = parseFloat(this.selectedRate);
            $('#priceTable tbody tr').each(function() {
                var basePrice = parseFloat($(this).find('.price').data('base-price'));
                var newPrice = (basePrice * rate).toFixed(2);
                $(this).find('.price').text(newPrice);
            });
        }
    }
});
</script>