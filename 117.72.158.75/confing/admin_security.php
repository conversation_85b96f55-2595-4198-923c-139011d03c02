<?php
/**
 * 管理员安全配置文件
 * 用于加强管理员功能的安全性
 */

if (!defined('IN_CRONLITE')) {
    die('Access denied');
}

/**
 * 验证管理员权限
 * @param int $required_level 需要的权限等级 (1=超级管理员, 2=普通管理员)
 * @return bool
 */
function check_admin_permission($required_level = 1) {
    global $islogin, $userrow;
    
    // 检查是否已登录
    if ($islogin != 1) {
        return false;
    }
    
    // 检查用户权限等级
    if (!isset($userrow['uid']) || $userrow['uid'] > $required_level) {
        return false;
    }
    
    return true;
}

/**
 * 记录管理员操作日志
 * @param string $action 操作类型
 * @param string $details 操作详情
 * @param array $data 相关数据
 */
function log_admin_action($action, $details, $data = []) {
    global $DB, $userrow, $clientip;
    
    $log_data = [
        'admin_uid' => $userrow['uid'],
        'admin_user' => $userrow['user'],
        'action' => $action,
        'details' => $details,
        'ip' => $clientip,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'data' => json_encode($data),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // 记录到管理员操作日志表
    $sql = "INSERT INTO qingka_wangke_admin_log (admin_uid, admin_user, action, details, ip, user_agent, data, addtime) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $params = [
        $log_data['admin_uid'],
        $log_data['admin_user'], 
        $log_data['action'],
        $log_data['details'],
        $log_data['ip'],
        $log_data['user_agent'],
        $log_data['data'],
        $log_data['timestamp']
    ];
    
    $DB->prepare_query($sql, $params);
}

/**
 * 安全的JSON响应
 * @param int $code 状态码
 * @param string $msg 消息
 * @param array $data 数据
 */
function secure_json_response($code, $msg, $data = null) {
    $response = [
        'code' => $code,
        'msg' => htmlspecialchars($msg, ENT_QUOTES, 'UTF-8'),
        'timestamp' => time()
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    header('Content-Type: application/json; charset=UTF-8');
    exit(json_encode($response, JSON_UNESCAPED_UNICODE));
}

/**
 * 验证输入参数
 * @param array $params 参数数组
 * @param array $rules 验证规则
 * @return array|bool 验证失败返回错误信息，成功返回true
 */
function validate_params($params, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $params[$field] ?? null;
        
        // 检查必填项
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[] = "{$field}不能为空";
            continue;
        }
        
        if (!empty($value)) {
            // 检查数据类型
            if (isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'int':
                        if (!is_numeric($value) || !ctype_digit($value)) {
                            $errors[] = "{$field}必须是整数";
                        }
                        break;
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[] = "{$field}格式不正确";
                        }
                        break;
                }
            }
            
            // 检查长度限制
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[] = "{$field}长度不能超过{$rule['max_length']}字符";
            }
            
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[] = "{$field}长度不能少于{$rule['min_length']}字符";
            }
        }
    }
    
    return empty($errors) ? true : $errors;
}

/**
 * 管理员操作频率限制
 * @param string $action 操作类型
 * @param int $limit 限制次数
 * @param int $window 时间窗口（秒）
 * @return bool
 */
function check_admin_rate_limit($action, $limit = 100, $window = 3600) {
    global $DB, $userrow;
    
    $sql = "SELECT COUNT(*) as count FROM qingka_wangke_admin_log WHERE admin_uid = ? AND action = ? AND addtime > ?";
    $params = [$userrow['uid'], $action, date('Y-m-d H:i:s', time() - $window)];
    
    $result = $DB->prepare_getrow($sql, $params);
    
    return ($result['count'] ?? 0) < $limit;
}

?>
