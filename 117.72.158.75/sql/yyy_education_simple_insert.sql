-- yyy教育货源简单插入脚本
-- 适用于已有表结构的情况
-- 创建时间: 2025-08-20

-- 1. 插入yyy教育货源配置（如果不存在）
INSERT IGNORE INTO `qingka_wangke_huoyuan` (
  `pt`, `name`, `url`, `user`, `pass`, `token`, `ip`, `cookie`, `status`, `addtime`
) VALUES (
  'yyy',
  'yyy教育 (1175+网站)',
  'http://*************:6966',
  '573749877',
  'liuyaxin123',
  '',
  '*************',
  '',
  1,
  NOW()
);

-- 2. 插入yyy教育分类（如果不存在）
INSERT IGNORE INTO `qingka_wangke_fenlei` (`name`, `text`, `sort`, `status`) VALUES
('yyy教育', 'yyy教育平台聚合1175+个教育网站，提供各类网课培训项目，包括教师培训、职业技能培训等。支持查课和下单功能。', 100, 1);

-- 3. 获取货源ID和分类ID
SET @huoyuan_id = (SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'yyy' ORDER BY hid DESC LIMIT 1);
SET @fenlei_id = (SELECT id FROM qingka_wangke_fenlei WHERE name = 'yyy教育' ORDER BY id DESC LIMIT 1);

-- 4. 插入示例商品（如果不存在）
INSERT IGNORE INTO `qingka_wangke_class` (
  `sort`, `name`, `getnoun`, `noun`, `price`, `queryplat`, `docking`, 
  `yunsuan`, `content`, `status`, `fenlei`
) VALUES
(100, 'yyy教育-合肥永君专业技术人员继续教育在线', '1683', '1683', 2.40, @huoyuan_id, @huoyuan_id, '*', '视频+考试 格式：账号 密码 培训名 (yyy教育聚合平台)', 1, @fenlei_id),
(99, 'yyy教育-通用查课下单', '2864', '2864', 2.00, @huoyuan_id, @huoyuan_id, '*', '支持1175+个教育网站 格式：账号 密码 课程名称 (使用lastoid参数)', 1, @fenlei_id),
(98, 'yyy教育-教师培训专用', '2000', '2000', 2.50, @huoyuan_id, @huoyuan_id, '*', '教师专业发展培训 格式：账号 密码 培训名称', 1, @fenlei_id);

-- 5. 显示配置完成信息
SELECT 
  '货源配置完成' as message,
  hid as 货源ID,
  name as 货源名称,
  url as API地址,
  user as 账号
FROM qingka_wangke_huoyuan 
WHERE pt = 'yyy';

SELECT 
  '分类配置完成' as message,
  id as 分类ID,
  name as 分类名称
FROM qingka_wangke_fenlei 
WHERE name = 'yyy教育';

SELECT 
  '商品配置完成' as message,
  COUNT(*) as 商品数量
FROM qingka_wangke_class 
WHERE fenlei = @fenlei_id;
