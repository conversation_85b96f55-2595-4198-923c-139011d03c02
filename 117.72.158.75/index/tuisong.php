<?php
$mod = 'blank';
$title = '推送设置';
require_once('head.php');
?>
<div class="app-content-body">
    <div class="wrapper-md control">
        <!-- 大卡片容器 -->
        <div class="panel panel-default" id="push-setting-card">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 10px 10px 0 0;">
                推送设置
            </div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 0 0 10px 10px;">
                <!-- 推送Token设置 -->
                <div class="form-group">
                    <label>微信搜索关注showdoc公众号，填写推送链接位于/push/后的最后一串{token}</label>
                    <input type="text" class="form-control" v-model="pushToken" placeholder="请输入推送TOKEN">
                </div>
                <div class="form-group">
                    <el-button type="primary" round @click="saveToken">保存TOKEN</el-button>
                    <el-button type="info" round @click="testToken" style="margin-left: 10px;">测试TOKEN</el-button>
                </div>
                <hr>
                <!-- 推送开关设置 -->
                <div class="push-switches">
                    <div class="switch-item">
                        <label>登录通知</label>
                        <el-switch v-model="settings.login_notification"></el-switch>
                    </div>
                    <div class="switch-item">
                        <label>工单提醒</label>
                        <el-switch v-model="settings.work_order_notification"></el-switch>
                    </div>
                    <!--<div class="switch-item">
                        <label>公告通知</label>
                        <el-switch v-model="settings.announcement_notification"></el-switch>
                    </div>-->
                    <div class="switch-item">
                        <label>API余额预警</label>
                        <el-switch v-model="settings.balance_warning"></el-switch>
                    </div>
                    <?php if ($userrow['uid'] == 1) { ?>
                    <div class="switch-item">
                        <label>站长货源余额预警 需开启货源监控</label>
                        <el-switch v-model="settings.supply_balance_warning"></el-switch>
                    </div>
                    <div class="switch-item">
                        <label>站长货源更新通知 需开启货源监控</label>
                        <el-switch v-model="settings.supply_update_notification"></el-switch>
                    </div>
                    <?php } ?>
                </div>
                <div class="form-group" style="margin-top: 20px;">
                    <el-button type="primary" round @click="saveSettings">保存设置</el-button>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
.push-switches .switch-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}
.push-switches .switch-item:last-child {
    border-bottom: none;
}
</style>
<?php
require_once("lightyearfooter.php");
require_once("footer.php");
?>
<script>
new Vue({
    el: ".app-content-body",
    data: {
        pushToken: '',
        settings: {
            login_notification: false,
            work_order_notification: false,
            announcement_notification: false,
            balance_warning: false,
            supply_balance_warning: false,
            supply_update_notification: false
        }
    },
    methods: {
        getToken() {
            axios.get("/apisub.php?act=getPushToken").then(response => {
                if (response.data.code == 1) {
                    this.pushToken = response.data.token || '';
                } else {
                    this.$message.error(response.data.msg);
                }
            }).catch(() => {
                this.$message.error('请求失败');
            });
        },
        saveToken() {
            if (!this.pushToken.trim()) {
                this.$message.warning('请输入推送TOKEN');
                return;
            }
            axios.get(`/apisub.php?act=savePushToken&token=${encodeURIComponent(this.pushToken)}`).then(response => {
                if (response.data.code == 1) {
                    this.$message.success(response.data.msg);
                    this.getToken();
                } else {
                    this.$message.error(response.data.msg);
                }
            }).catch(() => {
                this.$message.error('请求失败');
            });
        },
        testToken() {
            if (!this.pushToken.trim()) {
                this.$message.warning('请输入推送TOKEN');
                return;
            }
            axios.get(`/apisub.php?act=testPushToken&token=${encodeURIComponent(this.pushToken)}`).then(response => {
                if (response.data.code == 1) {
                    this.$message.success(response.data.msg);
                } else {
                    this.$message.error(response.data.msg);
                }
            }).catch(() => {
                this.$message.error('请求失败');
            });
        },
        getSettings() {
            axios.get("/apisub.php?act=getUserSetting").then(response => {
                if (response.data.code == 1) {
                    const backendSettings = response.data.settings;
                    this.settings = {
                        login_notification: !!backendSettings.login_notification,
                        work_order_notification: !!backendSettings.work_order_notification,
                        announcement_notification: !!backendSettings.announcement_notification,
                        balance_warning: !!backendSettings.balance_warning,
                        supply_balance_warning: !!backendSettings.supply_balance_warning,
                        supply_update_notification: !!backendSettings.supply_update_notification
                    };
                } else {
                    this.$message.error(response.data.msg);
                }
            }).catch(() => {
                this.$message.error('请求失败');
            });
        },
        saveSettings() {
            const params = new URLSearchParams();
            params.append('login_notification', this.settings.login_notification ? 1 : 0);
            params.append('work_order_notification', this.settings.work_order_notification ? 1 : 0);
            params.append('announcement_notification', this.settings.announcement_notification ? 1 : 0);
            params.append('balance_warning', this.settings.balance_warning ? 1 : 0);
            params.append('supply_balance_warning', this.settings.supply_balance_warning ? 1 : 0);
            params.append('supply_update_notification', this.settings.supply_update_notification ? 1 : 0);
            axios.post("/apisub.php?act=saveUserSetting", params).then(response => {
                if (response.data.code == 1) {
                    this.$message.success(response.data.msg);
                    this.getSettings();
                } else {
                    this.$message.error(response.data.msg);
                }
            }).catch(() => {
                this.$message.error('请求失败');
            });
        }
    },
    mounted() {
        this.getToken();
        this.getSettings();
    }
});
</script>