<?php  
function msWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,yid,user from qingka_wangke_order where oid='{$oid}' ");
	$hid = $b["hid"];
	$yid = $b["yid"];
	$user = $b["user"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];

	// yyy教育秒刷接口 - 该平台不支持秒刷功能
	if ($type == "yyy") {
		// yyy教育平台不支持秒刷订单功能
		// 返回不支持的提示信息
		return array("code" => -1, "msg" => "yyy教育平台不支持秒刷订单功能");
	}

  if ($type=="ikun" ){
$KUN_surl= $a["url"];
$KUN_url=$KUN_surl."/upTp/?token=".$yid."&tp=".urlencode("学习通(快刷)");
$result=get_url($KUN_url);
$result=json_decode($result,true);
if($result["code"]==1){
$b=array("code"=>1,"msg"=>$result["msg"]);}
else{
$b=array("code"=>-1,"msg"=>$result["msg"]);}
return$b;
}
	if ($type == "上游接口的type") {
$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
$uu_rl = $a["url"];
$uu_url = "$uu_rl/api.php?act=kuaishua";
$result = get_url($uu_url, $data);
$result = json_decode($result, true);
return $result;
}

	else {
				$b = array("code" => -1, "msg" => "接口异常，请联系管理员");
		}
}




?>