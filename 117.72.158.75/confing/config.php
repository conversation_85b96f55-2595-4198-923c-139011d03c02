<?php
/**
 * 核心代码保护，只为安全，绝无后门，天地可鉴。
 * 数据库连接设置在myqslset文件里。
 * 创建时间 2025-08-01 16:41:14
 * 严禁反编译、逆向等任何形式的侵权行为，违者将追究法律责任
**/
if (!defined('__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__')) define('__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__', '__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__');$GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__] = explode(':n:K:1', 'H*:n:K:16d7973716c7365742e706870:n:K:1646566696e6564:n:K:1494e5f43524f4e4c495445:n:K:17472696d:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:16578706c6f6465:n:K:10a5c:n:K:1636f756e74:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:166696c655f6765745f636f6e74656e7473:n:K:15f5f5f46494c455f5f5f:n:K:16578706c6f6465:n:K:10a0d:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:16578706c6f6465:n:K:16578706c6f6465:n:K:10d0a:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:12a20e69cace4bba3e7a081e794b120e69893e888aa504850456e436f64652056312e3120e5889be5bbba:n:K:1636f756e74:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:1737472737472:n:K:12a20e5889be5bbbae697b6e997b4:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:16578706c6f6465:n:K:10a:n:K:13c3f706870:n:K:1636f756e74:n:K:12a2a2f:n:K:12a20e4b8a5e7a681e58f8de7bc96e8af91e38081e98086e59091e7ad89e4bbbbe4bd95e5bda2e5bc8fe79a84e4beb5e69d83e8a18ce4b8baefbc8ce8bf9de88085e5b086e8bfbde7a9b6e6b395e5be8be8b4a3e4bbbb:n:K:12a20687474703a2f2f656e636f64652e627269362e636e2f:n:K:166696c655f657869737473:n:K:15f5f5f46494c455f5f5f:n:K:1636f756e74:n:K:12a20e68a80e69cafe694afe68c8120e69893e888aa203c323133363131383033394071712e636f6d3e:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:12f2a2a:n:K:16578706c6f6465:n:K:10d:n:K:1636f756e74:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:1e8afb7e58bbfe588a0e999a4e6b3a8e9878aefbc81:n:K:1636f756e74:n:K:16d7973716c695f7175657279:n:K:1736574206368617261637465722073657420277574663827:n:K:16d7973716c695f636f6e6e656374:n:K:16d7973716c695f7175657279:n:K:1736574206e616d657320277574663827:n:K:16d7973716c695f7175657279:n:K:17365742073716c5f6d6f6465203d202727:n:K:1436f6e6e656374204572726f722028:n:K:16d7973716c695f636f6e6e6563745f6572726e6f:n:K:12920:n:K:16d7973716c695f636f6e6e6563745f6572726f72:n:K:16d7973716c695f70696e67:n:K:163616c6c5f757365725f66756e635f6172726179:n:K:162696e645f706172616d:n:K:169:n:K:169735f696e74:n:K:173:n:K:164:n:K:169735f646f75626c65:n:K:16d7973716c695f66657463685f6173736f63:n:K:16d7973716c695f7175657279:n:K:16d7973716c695f66657463685f6173736f63:n:K:16d7973716c695f667265655f726573756c74:n:K:16d7973716c695f66657463685f6172726179:n:K:16d7973716c695f7175657279:n:K:16d7973716c695f667265655f726573756c74:n:K:16d7973716c695f7175657279:n:K:16d7973716c695f7265616c5f6573636170655f737472696e67:n:K:16d7973716c695f61666665637465645f726f7773:n:K:16d7973716c695f6572726f72:n:K:15b:n:K:15d20:n:K:16d7973716c695f6572726e6f:n:K:16d7973716c695f636c6f7365');goto XwTqw; KL95_: include call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 7 - 2) + -12], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 7 + 4) + 3]); goto Dp300; Dp300: if (call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 - 4 + 1) + -3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 - 1 - 3) + -4]), call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 9 - 3) + 4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 + 9 + 2) + -18]))) { goto Kht5z; } goto pgq_5; pgq_5: exit; goto ZmcFz; ZmcFz: Kht5z: goto wD_W0; XwTqw: (function () { goto qnQvI; XXgw9: foreach ($__lines__ as $key => $value) { $__lines__[$key] = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 - 6 - 1) + 1], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 1 - 8) + 12]), $value); Xu_C2: } goto sfutV; ruiKv: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 7 + 5) + -20], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 10 + 6) + -20]) . $__lines__[3]; goto OU9Ad; au1_F: $__lines__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 7 - 3) + 1], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 9 + 7) + -17]), call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 10 + 9) + -3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 4 + 9) + 0]), $__file_contents__); goto JQvwN; D1KNe: tc0O9: goto Gy_ix; Gunqc: exit; goto Ua37D; xH_Lz: if (!(call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 - 1 + 1) + -3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 7 - 5) + 18]), $__lines__) < 8)) { goto tc0O9; } goto tLSuz; tPvNp: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 3 + 10) + -12], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 4 - 4) + 9]) . $__lines__[4]; goto Gunqc; RopDO: $__file_contents__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 1 + 7) + -14], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 5 + 1) + -5]), $GLOBALS[call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 5 - 8) + 12], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 4 + 6) + -7])]); goto EVdrV; E5Lpq: $__lines__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 - 1 + 6) + -15], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 + 7 + 2) + -7]), call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 9 + 3) + -20], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 7 + 3) + 10]), $__file_contents__); goto aIEre; pFd6l: nC9_b: goto xGxNv; rYzia: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 8 + 4) + 2], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 8 + 5) + -6]) . $__lines__[2]; goto O7qMS; j3GXa: exit; goto LYFsh; O7qMS: exit; goto GJ9Kl; EVdrV: $__lines__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 3 + 6) + -15], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 4 - 5) + 23]), PHP_EOL, $__file_contents__); goto Y6VX3; MBjvk: $__lines__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 1 + 9) + -17], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 + 9 - 5) + 9]), call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 1 - 2) + -5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 3 + 6) + 9]), $__file_contents__); goto MbOda; OE1kU: bN85Q: goto A27ap; JQvwN: FrmfG: goto xH_Lz; wWjPL: pYtY1: goto T6N6J; nfFFV: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 10 - 5) + 6], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 - 8 + 8) + 12]) . $__lines__[6]; goto mm_hB; LYFsh: ZrF56: goto XTc_f; SrsB_: exit; goto pFd6l; Ua37D: W1Rvl: goto PNYpG; xgosi: return; goto lb_BX; xGxNv: if (!($__lines__[2] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 3 + 8) + -7], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 + 3 + 8) + 4]))) { goto GWAjM; } goto rYzia; RnfJ5: hftfD: goto h7eUm; mm_hB: exit; goto wWjPL; GJ9Kl: GWAjM: goto gncF2; F83xz: if (!(call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 + 5 - 2) + -13], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 + 10 + 2) + 5]), $__lines__) < 8)) { goto qt50Y; } goto L12Ks; f9Uyb: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 + 7 + 2) + -11], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 + 9 - 5) + 7]) . $__lines__[0]; goto j3GXa; gncF2: if (call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 6 + 2) + -17], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 3 - 8) + 22]), $__lines__[3], call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 10 - 8) + 17], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 6 + 1) + 11]))) { goto hftfD; } goto ruiKv; TS9WW: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 4 + 6) + -15], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 9 + 4) + 3]) . $__lines__[7]; goto kdTfa; kdTfa: exit; goto rvllc; MFA8a: rTCLp: goto TBdKv; WHWhV: exit; goto Bvcba; ncImG: $__lines__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 7 - 6) + -9], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 + 8 - 4) + 18]), call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 7 - 9) + 9], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 6 + 4) + 10]), $__file_contents__); goto OE1kU; rKQPa: if (!($__lines__[0] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 9 + 10) + -2], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 6 - 8) + 23]))) { goto ZrF56; } goto f9Uyb; Bvcba: qt50Y: goto XXgw9; IlTlz: if (!(call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 + 8 - 3) + -9], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 + 2 + 7) + 17]), $__lines__) < 8)) { goto l8ijQ; } goto E5Lpq; rvllc: kMqUJ: goto XEyoQ; T6N6J: if (!($__lines__[7] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 1 - 8) + 4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 1 + 2) + 19]))) { goto kMqUJ; } goto TS9WW; PNYpG: if (!($__lines__[5] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 8 + 5) + -21], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 9 + 4) + 26]))) { goto rTCLp; } goto C6PJT; lb_BX: Mlhul: goto RopDO; TBdKv: if (!($__lines__[6] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 + 2 - 7) + 3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 1 - 5) + 28]))) { goto pYtY1; } goto nfFFV; qnQvI: if (call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 10 - 9) + -9], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 2 - 2) + 29]), $GLOBALS[call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 10 + 8) + -5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 8 + 10) + 24])])) { goto Mlhul; } goto xgosi; Gy_ix: if (!(call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 - 10 + 10) + -3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 8 - 4) + 39]), $__lines__) < 8)) { goto bN85Q; } goto ncImG; h7eUm: if (!($__lines__[4] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 4 + 10) + -10], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 8 - 5) + 26]))) { goto W1Rvl; } goto tPvNp; MbOda: i4yO9: goto IlTlz; mUTSz: exit; goto MFA8a; Ln6k8: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 + 7 - 6) + -3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 6 + 6) + 18]) . $__lines__[1]; goto SrsB_; XTc_f: if (!($__lines__[1] != call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 + 3 + 6) + -19], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 7 + 10) + 32]))) { goto nC9_b; } goto Ln6k8; tLSuz: $__lines__ = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 3 - 9) + 5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 10 + 2) + 17]), call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 8 - 3) + 7], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 4 - 2) + 30]), $__file_contents__); goto D1KNe; A27ap: if (!(call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 4 - 9) + 4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 6 - 4) + 42]), $__lines__) < 8)) { goto i4yO9; } goto MBjvk; C6PJT: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 7 + 7) + -4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 4 - 2) + 30]) . $__lines__[5]; goto mUTSz; OU9Ad: exit; goto RnfJ5; L12Ks: echo call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 2 - 8) + 0], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 2 + 5) + 30]); goto WHWhV; sfutV: KJ3yz: goto rKQPa; aIEre: l8ijQ: goto F83xz; Y6VX3: if (!(call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 7 + 2) + -18], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 10 - 7) + 35]), $__lines__) < 8)) { goto FrmfG; } goto au1_F; XEyoQ: })(); goto KL95_; wD_W0: class DB { var $link = null; var $db_host; var $db_user; var $db_pass; var $db_name; var $db_port; function __construct($db_host, $db_user, $db_pass, $db_name, $db_port) { goto FsfY2; rSopj: $this->db_name = $db_name; goto q2W7o; q2W7o: $this->db_port = $db_port; goto f5N2d; f5N2d: $this->connect(); goto f3wgE; Wbce5: $this->db_pass = $db_pass; goto rSopj; FsfY2: $this->db_host = $db_host; goto rNEzr; rNEzr: $this->db_user = $db_user; goto Wbce5; f3wgE: } private function connect() { goto ls9us; MrBQA: z06zg: goto fcLIq; mMA4X: call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 2 + 7) + -18], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 3 + 6) + 32]), $this->link, call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 4 + 1) + -11], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 6 + 10) + 39])); goto LpxAq; La_hg: return true; goto IjyBD; E1aSx: if ($this->link) { goto z06zg; } goto H8PAE; ls9us: $this->link = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 1 + 2) + -12], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 2 - 8) + 45]), $this->db_host, $this->db_user, $this->db_pass, $this->db_name, $this->db_port); goto E1aSx; LpxAq: call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 + 10 + 7) + -20], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 10 + 6) + 47]), $this->link, call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 4 + 3) + -1], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 + 7 + 5) + 26])); goto La_hg; fcLIq: call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 4 + 7) + -4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 1 - 8) + 50]), $this->link, call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 - 8 - 7) + 5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 5 + 9) + 29])); goto mMA4X; H8PAE: die(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 + 8 + 9) + -18], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 7 - 7) + 57]) . call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 3 + 3) + -5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 7 + 6) + 48])) . call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 7 + 1) + 1], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 5 + 8) + 43]) . call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 - 6 + 6) + -10], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 7 + 5) + 35]))); goto MrBQA; IjyBD: } private function checkConnection() { goto iOEMG; INJab: $this->close(); goto f2Q0A; f2Q0A: $this->connect(); goto DzLav; DzLav: RKSav: goto G5oNq; iOEMG: if (call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 - 4 + 7) + -6], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 1 - 10) + 57]), $this->link)) { goto RKSav; } goto INJab; G5oNq: } function prepare_getrow($sql, $params = []) { goto Sz50l; msb9C: return $row; goto hD9yF; jbocq: $row = $result->fetch_assoc(); goto M76VP; ImKXW: $result = $stmt->get_result(); goto jbocq; cVCFP: if (!($stmt === false)) { goto sELeN; } goto U3wdH; M76VP: $stmt->close(); goto msb9C; ffcVo: $stmt = $this->prepare_query($sql, $params); goto cVCFP; Sz50l: $this->checkConnection(); goto ffcVo; mZB2c: sELeN: goto ImKXW; U3wdH: return false; goto mZB2c; hD9yF: } function prepare_count($sql, $params = []) { goto UzUoH; nijgY: $stmt = $this->prepare_query($sql, $params); goto IMGyq; UzUoH: $this->checkConnection(); goto nijgY; zFNw3: $result = $stmt->get_result(); goto xKOJe; xKOJe: $count = $result->fetch_array(); goto K1uEO; ASqsH: return $count[0]; goto wTQpT; xp2zJ: return false; goto opy1f; opy1f: b4AlJ: goto zFNw3; K1uEO: $stmt->close(); goto ASqsH; IMGyq: if (!($stmt === false)) { goto b4AlJ; } goto xp2zJ; wTQpT: } function prepare_query($sql, $params = []) { goto qH6_c; aL3G0: $bind_params = []; goto Z_7oF; vNrkU: $stmt = $this->link->prepare($sql); goto kWPkB; m9NMI: if (empty($params)) { goto A62rD; } goto KjnnC; HqXsy: sJsp9: goto GEUQF; e3LkH: A62rD: goto mFd0j; kWPkB: if (!($stmt === false)) { goto tsPkW; } goto ZwTq7; GEUQF: array_unshift($bind_params, $types); goto iv2TD; RNMse: return $stmt; goto rpxJL; qH6_c: $this->checkConnection(); goto vNrkU; mFd0j: $stmt->execute(); goto RNMse; KjnnC: $types = ''; goto aL3G0; ZwTq7: return false; goto y3k9D; y3k9D: tsPkW: goto m9NMI; iv2TD: call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 7 - 7) + -5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 2 - 7) + 60]), [$stmt, call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 7 + 8) + -24], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 + 3 + 4) + 46])], $bind_params); goto e3LkH; Z_7oF: foreach ($params as $param) { goto uNseP; RM7uk: q7F35: goto lqNln; lqNln: $types .= call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 7 + 7) + -7], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 4 - 7) + 55]); goto rv4oZ; uNseP: if (call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 7 - 10) + -5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 7 + 3) + 44]), $param)) { goto q7F35; } goto LEst9; FwN24: E9F0D: goto oT8QO; VfDsW: $bind_params[] = $param; goto FwN24; jLXIz: EliBY: goto jgeEc; LpXU7: $types .= call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(10 - 6 + 6) + -10], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 - 3 - 9) + 69]); goto ZUrbZ; M51M_: LiTMe: goto VfDsW; jgeEc: $types .= call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(2 - 2 - 4) + 4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 4 + 2) + 48]); goto M51M_; rv4oZ: goto LiTMe; goto jLXIz; ZUrbZ: goto LiTMe; goto RM7uk; LEst9: if (call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 1 - 3) + 0], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 3 - 5) + 57]), $param)) { goto EliBY; } goto LpXU7; oT8QO: } goto HqXsy; rpxJL: } function fetch($q) { return call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 8 - 2) + 6], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 6 - 8) + 59]), $q); } function get_row($q) { goto gLfhs; gLfhs: $this->checkConnection(); goto yuT57; v8IZO: mZEmM: goto MmX2P; yuT57: $result = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 - 6 - 2) + 2], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 5 - 10) + 71]), $this->link, $q); goto v33BE; IGG1c: return false; goto v8IZO; fM3QA: return $row; goto AAlZ3; v33BE: if (!($result === false)) { goto mZEmM; } goto IGG1c; MmX2P: $row = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 3 - 9) + -1], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 - 6 - 5) + 71]), $result); goto E_mFn; E_mFn: call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 + 9 - 1) + -9], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 10 + 9) + 59]), $result); goto fM3QA; AAlZ3: } function count($q) { goto Q5BJq; c0F1C: $count = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 - 8 - 1) + 3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 5 + 4) + 60]), $result); goto PF8mm; z5Gj_: $result = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 8 - 8) + -6], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 6 + 2) + 51]), $this->link, $q); goto UeT8d; wz7Wy: qXTHq: goto c0F1C; jbLqy: return $count[0]; goto aUNDo; PF8mm: call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(1 - 10 + 5) + 4], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 + 3 + 2) + 57]), $result); goto jbLqy; L9_Mr: return false; goto wz7Wy; UeT8d: if (!($result === false)) { goto qXTHq; } goto L9_Mr; Q5BJq: $this->checkConnection(); goto z5Gj_; aUNDo: } function query($q) { $this->checkConnection(); return call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(5 + 8 + 2) + -15], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 2 + 3) + 60]), $this->link, $q); } function escape($str) { $this->checkConnection(); return call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 + 3 + 7) + -14], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(6 + 5 - 4) + 64]), $this->link, $str); } function affected() { $this->checkConnection(); return call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 5 - 3) + -10], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 8 + 8) + 65]), $this->link); } function error() { goto jMk0D; jMk0D: $error = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 - 7 - 5) + 3], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 - 2 - 7) + 79]), $this->link); goto FEDBI; Ygbcn: return call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 3 + 7) + -19], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 - 6 + 6) + 66]) . $errno . call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(8 + 4 - 7) + -5], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 6 - 4) + 81]) . $error; goto IeRUR; FEDBI: $errno = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(4 - 10 + 6) + 0], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(3 + 7 + 8) + 58]), $this->link); goto Ygbcn; IeRUR: } function close() { goto LJ97d; LJ97d: if (!$this->link) { goto eHXKU; } goto PF8a2; X7lyA: return $q; goto CtFWs; yGB0b: $this->link = null; goto X7lyA; PF8a2: $q = call_user_func(call_user_func('pack', $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(7 - 6 + 1) + -2], $GLOBALS[__AA__648215133C98DE2ADBF8D2D29BDAA363__AA__][(9 + 5 + 6) + 57]), $this->link); goto yGB0b; BYxdi: return true; goto qD_Zz; CtFWs: eHXKU: goto BYxdi; qD_Zz: } }
