<?php
include('../confing/common.php');
$redis = new Redis();
$redis->connect("127.0.0.1", "6379");
$redis->select(10);
$processId = getenv('SUPERVISOR_PROCESS_NAME');

while (true) {
    $oid = $redis->lpop('addoid');
    $lenth = $redis->LLEN('addoid');
    if ($oid === false) {
        sleep(5);
        continue;
    }
    $b = $DB->prepare_getrow("SELECT * FROM qingka_wangke_order WHERE `oid`=?", [$oid]);
    if ($b) {
        if ($b['dockstatus'] == 5) {
            if ($b['user'] == "1") {
                $DB->prepare_query(
                    "UPDATE qingka_wangke_order SET `status`='请检查账号',`dockstatus`=2 WHERE oid=?",
                    [$b['oid']]
                );
            } else if ($b['school'] == "") {
                $DB->prepare_query(
                    "UPDATE qingka_wangke_order SET `status`='请检查学校名字',`dockstatus`=2 WHERE oid=?",
                    [$b['oid']]
                );
            } else if ($b['user'] == "") {
                $DB->prepare_query(
                    "UPDATE qingka_wangke_order SET `status`='请检查账号',`dockstatus`=2 WHERE oid=?",
                    [$b['oid']]
                );
            } else if ($b['kcname'] == "账号密码错误") {
                $DB->prepare_query(
                    "UPDATE qingka_wangke_order SET `status`='请检查账号密码',`dockstatus`=2 WHERE oid=?",
                    [$b['oid']]
                );
            } else {
                $result = addWk($b['oid']);
            }
            
            $today_day = date("Y-m-d H:i:s");
            
            if ($result['code'] == '1') {
                echo "当前进程 $processId 成功 uid：{$b['oid']}\r\n返回：{$result['msg']}\r\n队列池剩余：$lenth\r\n本次出队时间：$today_day\r\n------------------------------------------------------\r\n";
                
                $DB->prepare_query(
                    "UPDATE qingka_wangke_order SET `status`='上号中',`dockstatus`=1,`yid`=? WHERE oid=?",
                    [$result['yid'], $b['oid']]
                );
            } else {
                $DB->prepare_query(
                    "UPDATE qingka_wangke_order SET `dockstatus`=2 WHERE oid=?",
                    [$b['oid']]
                );
                echo "当前进程 $processId 失败 uid：{$b['oid']}\r\n失败原因：{$result['msg']}\r\n队列池剩余：$lenth\r\n本次出队时间：$today_day\r\n------------------------------------------------------\r\n";
            }
        } else {
            echo "当前进程 $processId 订单 oid: {$b['oid']} 状态错误 未进入提交状态\r\n";
        }
    } else {
        echo "当前进程 $processId 错误，原因：不存在$oid 请修复数据库\r\n";
    }

    sleep(3);
}
?>