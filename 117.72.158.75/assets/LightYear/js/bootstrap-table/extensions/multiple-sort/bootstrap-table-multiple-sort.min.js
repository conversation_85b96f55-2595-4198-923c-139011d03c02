/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableMultipleSort={exports:{}}.exports}})(this,function(){'use strict';var a='function'==typeof Symbol&&'symbol'==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&'function'==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?'symbol':typeof a};(function(d){var b=!1,c=function(c){var e=c.sortModalSelector,f='#'+e;if(!d(f).hasClass('modal')){var g='  <div class="modal fade" id="'+e+'" tabindex="-1" role="dialog" aria-labelledby="'+e+'Label" aria-hidden="true">';g+='         <div class="modal-dialog">',g+='             <div class="modal-content">',g+='                 <div class="modal-header">',g+='                     <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>',g+='                     <h4 class="modal-title" id="'+e+'Label">'+c.options.formatMultipleSort()+'</h4>',g+='                 </div>',g+='                 <div class="modal-body">',g+='                     <div class="bootstrap-table">',g+='                         <div class="fixed-table-toolbar">',g+='                             <div class="bars">',g+='                                 <div id="toolbar">',g+='                                     <button id="add" type="button" class="btn btn-default"><i class="'+c.options.iconsPrefix+' '+c.options.icons.plus+'"></i> '+c.options.formatAddLevel()+'</button>',g+='                                     <button id="delete" type="button" class="btn btn-default" disabled><i class="'+c.options.iconsPrefix+' '+c.options.icons.minus+'"></i> '+c.options.formatDeleteLevel()+'</button>',g+='                                 </div>',g+='                             </div>',g+='                         </div>',g+='                         <div class="fixed-table-container">',g+='                             <table id="multi-sort" class="table">',g+='                                 <thead>',g+='                                     <tr>',g+='                                         <th></th>',g+='                                         <th><div class="th-inner">'+c.options.formatColumn()+'</div></th>',g+='                                         <th><div class="th-inner">'+c.options.formatOrder()+'</div></th>',g+='                                     </tr>',g+='                                 </thead>',g+='                                 <tbody></tbody>',g+='                             </table>',g+='                         </div>',g+='                     </div>',g+='                 </div>',g+='                 <div class="modal-footer">',g+='                     <button type="button" class="btn btn-default" data-dismiss="modal">'+c.options.formatCancel()+'</button>',g+='                     <button type="button" class="btn btn-primary">'+c.options.formatSort()+'</button>',g+='                 </div>',g+='             </div>',g+='         </div>',g+='     </div>',d('body').append(d(g)),c.$sortModal=d(f);var h=c.$sortModal.find('tbody > tr');if(c.$sortModal.off('click','#add').on('click','#add',function(){var a=c.$sortModal.find('.multi-sort-name:first option').length,b=c.$sortModal.find('tbody tr').length;b<a&&(b++,c.addLevel(),c.setButtonStates())}),c.$sortModal.off('click','#delete').on('click','#delete',function(){var a=c.$sortModal.find('.multi-sort-name:first option').length,b=c.$sortModal.find('tbody tr').length;1<b&&b<=a&&(b--,c.$sortModal.find('tbody tr:last').remove(),c.setButtonStates())}),c.$sortModal.off('click','.btn-primary').on('click','.btn-primary',function(){var a=c.$sortModal.find('tbody > tr'),e=c.$sortModal.find('div.alert'),f=[],g=[];c.options.sortPriority=d.map(a,function(a){var b=d(a),c=b.find('.multi-sort-name').val(),e=b.find('.multi-sort-order').val();return f.push(c),{sortName:c,sortOrder:e}});for(var h=f.sort(),j=0;j<f.length-1;j++)h[j+1]==h[j]&&g.push(h[j]);if(0<g.length)0===e.length&&(e='<div class="alert alert-danger" role="alert"><strong>'+c.options.formatDuplicateAlertTitle()+'</strong> '+c.options.formatDuplicateAlertDescription()+'</div>',d(e).insertBefore(c.$sortModal.find('.bars')));else{if(1===e.length&&d(e).remove(),c.$sortModal.modal('hide'),c.options.sortName='','server'===c.options.sidePagination){var i=c.options.queryParams;return c.options.queryParams=function(a){return a.multiSort=c.options.sortPriority,i(a)},b=!1,void c.initServer(c.options.silentSort)}c.onMultipleSort()}}),(null===c.options.sortPriority||0===c.options.sortPriority.length)&&c.options.sortName&&(c.options.sortPriority=[{sortName:c.options.sortName,sortOrder:c.options.sortOrder}]),!(null!==c.options.sortPriority&&0<c.options.sortPriority.length))c.addLevel(0);else if(h.length<c.options.sortPriority.length&&'object'===a(c.options.sortPriority))for(var j=0;j<c.options.sortPriority.length;j++)c.addLevel(j,c.options.sortPriority[j]);c.setButtonStates()}};d.fn.bootstrapTable.methods.push('multipleSort'),d.extend(d.fn.bootstrapTable.defaults,{showMultiSort:!1,showMultiSortButton:!0,sortPriority:null,onMultipleSort:function(){return!1}}),d.extend(d.fn.bootstrapTable.defaults.icons,{sort:'glyphicon-sort',plus:'glyphicon-plus',minus:'glyphicon-minus'}),d.extend(d.fn.bootstrapTable.Constructor.EVENTS,{"multiple-sort.bs.table":'onMultipleSort'}),d.extend(d.fn.bootstrapTable.locales,{formatMultipleSort:function(){return'Multiple Sort'},formatAddLevel:function(){return'Add Level'},formatDeleteLevel:function(){return'Delete Level'},formatColumn:function(){return'Column'},formatOrder:function(){return'Order'},formatSortBy:function(){return'Sort by'},formatThenBy:function(){return'Then by'},formatSort:function(){return'Sort'},formatCancel:function(){return'Cancel'},formatDuplicateAlertTitle:function(){return'Duplicate(s) detected!'},formatDuplicateAlertDescription:function(){return'Please remove or change any duplicate column.'},formatSortOrders:function(){return{asc:'Ascending',desc:'Descending'}}}),d.extend(d.fn.bootstrapTable.defaults,d.fn.bootstrapTable.locales);var e=d.fn.bootstrapTable.Constructor,f=e.prototype.initToolbar;e.prototype.initToolbar=function(){this.showToolbar=this.showToolbar||this.options.showMultiSort;var e=this,g='sortModal_'+this.$el.attr('id'),h='#'+g;if(this.$sortModal=d(h),this.sortModalSelector=g,f.apply(this,Array.prototype.slice.apply(arguments)),'server'===e.options.sidePagination&&!b&&null!==e.options.sortPriority){var i=e.options.queryParams;e.options.queryParams=function(a){return a.multiSort=e.options.sortPriority,i(a)}}if(this.options.showMultiSort){var j=this.$toolbar.find('>.btn-group').first(),k=this.$toolbar.find('div.multi-sort');!k.length&&this.options.showMultiSortButton&&(k='  <button class="multi-sort btn btn-default'+(void 0===this.options.iconSize?'':' btn-'+this.options.iconSize)+'" type="button" data-toggle="modal" data-target="'+h+'" title="'+this.options.formatMultipleSort()+'">',k+='     <i class="'+this.options.iconsPrefix+' '+this.options.icons.sort+'"></i>',k+='</button>',j.append(k),c(e)),this.$el.on('sort.bs.table',function(){b=!0}),this.$el.on('multiple-sort.bs.table',function(){b=!1}),this.$el.on('load-success.bs.table',function(){b||null===e.options.sortPriority||'object'!==a(e.options.sortPriority)||'server'===e.options.sidePagination||e.onMultipleSort()}),this.$el.on('column-switch.bs.table',function(a,b){for(var d=0;d<e.options.sortPriority.length;d++)e.options.sortPriority[d].sortName===b&&e.options.sortPriority.splice(d,1);e.assignSortableArrows(),e.$sortModal.remove(),c(e)}),this.$el.on('reset-view.bs.table',function(){b||null===e.options.sortPriority||'object'!==a(e.options.sortPriority)||e.assignSortableArrows()})}},e.prototype.multipleSort=function(){var c=this;b||null===c.options.sortPriority||'object'!==a(c.options.sortPriority)||'server'===c.options.sidePagination||c.onMultipleSort()},e.prototype.onMultipleSort=function(){var c=this,e=function(a,b){return a>b?1:a<b?-1:0},f=function(f,a){for(var b=[],g=[],h=0;h<c.options.sortPriority.length;h++){var i='desc'===c.options.sortPriority[h].sortOrder?-1:1,j=f[c.options.sortPriority[h].sortName],k=a[c.options.sortPriority[h].sortName];(j===void 0||null===j)&&(j=''),(k===void 0||null===k)&&(k=''),d.isNumeric(j)&&d.isNumeric(k)&&(j=parseFloat(j),k=parseFloat(k)),'string'!=typeof j&&(j=j.toString()),b.push(i*e(j,k)),g.push(i*e(k,j))}return e(b,g)};this.data.sort(function(c,a){return f(c,a)}),this.initBody(),this.assignSortableArrows(),this.trigger('multiple-sort')},e.prototype.addLevel=function(a,b){var c=0===a?this.options.formatSortBy():this.options.formatThenBy();this.$sortModal.find('tbody').append(d('<tr>').append(d('<td>').text(c)).append(d('<td>').append(d('<select class="form-control multi-sort-name">'))).append(d('<td>').append(d('<select class="form-control multi-sort-order">'))));var e=this.$sortModal.find('.multi-sort-name').last(),f=this.$sortModal.find('.multi-sort-order').last();d.each(this.columns,function(a,b){return!1===b.sortable||!1===b.visible||void e.append('<option value="'+b.field+'">'+b.title+'</option>')}),d.each(this.options.formatSortOrders(),function(a,b){f.append('<option value="'+a+'">'+b+'</option>')}),b!==void 0&&(e.find('option[value="'+b.sortName+'"]').attr('selected',!0),f.find('option[value="'+b.sortOrder+'"]').attr('selected',!0))},e.prototype.assignSortableArrows=function(){for(var a=this,b=a.$header.find('th'),e=0;e<b.length;e++)for(var f=0;f<a.options.sortPriority.length;f++)d(b[e]).data('field')===a.options.sortPriority[f].sortName&&d(b[e]).find('.sortable').removeClass('desc asc').addClass(a.options.sortPriority[f].sortOrder)},e.prototype.setButtonStates=function(){var a=this.$sortModal.find('.multi-sort-name:first option').length,b=this.$sortModal.find('tbody tr').length;b==a&&this.$sortModal.find('#add').attr('disabled','disabled'),1<b&&this.$sortModal.find('#delete').removeAttr('disabled'),b<a&&this.$sortModal.find('#add').removeAttr('disabled'),1==b&&this.$sortModal.find('#delete').attr('disabled','disabled')}})(jQuery)});