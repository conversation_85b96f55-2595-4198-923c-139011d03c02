<?php
$title='货源设置';
require_once('head.php');
if($userrow['uid']!=1){
setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
exit("<script language='javascript'>window.location.href='login.php';</script>");}
?>
<div class="app-content-body ">
    <div class="wrapper-md control" id="orderlist">
        <div class="panel panel-default" >
            <div class="panel-heading font-bold layui-bg-black">平台列表&nbsp;<button class="btn btn-xs btn-light" data-toggle="modal" data-target="#modal-add">添加</button></div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead><tr><th>操作</th><th>ID</th><th>名称</th><th>平台</th><th>账号</th><th>密码</th><th>网址</th><th>密钥/token</th><th>添加时间</th></tr></thead>
                        <tbody>
                            <tr v-for="res in row.data">
                                <td>
                                    <button class="btn btn-xs btn-primary" data-toggle="modal" data-target="#modal-update" @click="storeInfo=res">编辑</button>&nbsp;
                                    <button class="btn btn-xs btn-danger" data-toggle="modal" data-target="#modal-delete" @click="storeInfo=res">删除</button>&nbsp;
                                </td>
                                <td>{{res.hid}}</td>
                                <td>{{res.name}}</td>
                                <td>{{res.pt}}</td>
                                <td>{{res.user}}</td>
                                <td>{{res.pass}}</td>
                                <td>{{res.url}}</td>
                                <td>{{res.token}}</td>
                                <td>{{res.addtime}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <ul class="pagination" v-if="row.last_page>1">
                    <li class="disabled"><a @click="get(1)">首页</a></li>
                    <li class="disabled"><a @click="row.current_page>1?get(row.current_page-1):''">&laquo;</a></li>
                    <li @click="get(row.current_page-3)" v-if="row.current_page-3>=1"><a>{{ row.current_page-3 }}</a></li>
                    <li @click="get(row.current_page-2)" v-if="row.current_page-2>=1"><a>{{ row.current_page-2 }}</a></li>
                    <li @click="get(row.current_page-1)" v-if="row.current_page-1>=1"><a>{{ row.current_page-1 }}</a></li>
                    <li :class="{'active':row.current_page==row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
                    <li @click="get(row.current_page+1)" v-if="row.current_page+1<=row.last_page"><a>{{ row.current_page+1 }}</a></li>
                    <li @click="get(row.current_page+2)" v-if="row.current_page+2<=row.last_page"><a>{{ row.current_page+2 }}</a></li>
                    <li @click="get(row.current_page+3)" v-if="row.current_page+3<=row.last_page"><a>{{ row.current_page+3 }}</a></li>
                    <li class="disabled"><a @click="row.last_page>row.current_page?get(row.current_page+1):''">&raquo;</a></li>
                    <li class="disabled"><a @click="get(row.last_page)">尾页</a></li>
                </ul>
            </div>
        </div>

        <div class="modal fade primary" id="modal-update">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">平台修改</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" id="form-update">
                            <input type="hidden" name="action" value="update"/>
                            <input type="hidden" name="hid" :value="storeInfo.hid"/>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">名称</label>
                                <div class="col-sm-9">
                                    <input type="text" name="name" class="layui-input" :value="storeInfo.name" placeholder="输入自定义名称">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">平台</label>
                                <div class="col-sm-9">
                                    <select name="pt" class="layui-select" :value="storeInfo.pt" style="background: url('../index/arrow.png') no-repeat scroll 99%; width:100%">
                                        <?php
                                        $a=wkname();
                                        foreach($a as $key => $value){
                                            echo '<option value="'.$key.'">'.$value.'</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">域名</label>
                                <div class="col-sm-9">
                                    <input type="text" name="url" class="layui-input" :value="storeInfo.url" placeholder="输入域名">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">账号</label>
                                <div class="col-sm-9">
                                    <input type="text" name="user" class="layui-input" :value="storeInfo.user" placeholder="输入账号,27对接填uid">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">密码</label>
                                <div class="col-sm-9">
                                    <input type="text" name="pass" class="layui-input" :value="storeInfo.pass" placeholder="输入密码,27对接填key">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">密匙/token</label>
                                <div class="col-sm-9">
                                    <input type="text" name="token" class="layui-input" :value="storeInfo.token" placeholder="输入密匙/token，没有则不输入">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">cookie</label>
                                <div class="col-sm-9">
                                    <textarea name="cookie" placeholder="没必要，不用输入" class="layui-textarea" :value="storeInfo.cookie" rows="4"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="layui-btn layui-btn-danger" data-dismiss="modal">取消</button>
                        <button type="button" class="layui-btn" @click="form('update')">确定</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade primary" id="modal-add">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">平台添加</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" id="form-add">
                            <input type="hidden" name="action" value="add"/>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">名称</label>
                                <div class="col-sm-9">
                                    <input type="text" name="name" class="form-control" placeholder="输入自定义名称">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">平台</label>
                                <div class="col-sm-9">
                                    <select name="pt" class="form-control">
                                        <?php
                                        $a=wkname();
                                        foreach($a as $key => $value){
                                            echo '<option value="'.$key.'">'.$value.'</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">域名</label>
                                <div class="col-sm-9">
                                    <input type="text" name="url" class="form-control" placeholder="输入域名">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">账号</label>
                                <div class="col-sm-9">
                                    <input type="text" name="user" class="form-control" placeholder="输入账号,27/29对接填uid">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">密码</label>
                                <div class="col-sm-9">
                                    <input type="text" name="pass" class="form-control" placeholder="输入密码,27/29对接填key">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">密匙/token</label>
                                <div class="col-sm-9">
                                    <input type="text" name="token" class="form-control" placeholder="输入密匙/token，没有则不输入">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">cookie</label>
                                <div class="col-sm-9">
                                    <textarea name="cookie" placeholder="没必要，不用输入" class="form-control" rows="4"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" @click="form('add')">确定</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade primary" id="modal-delete">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">安全风险警告</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" id="form-delete">
                            <input type="hidden" name="action" value="huoyuandel"/>
                            <input type="hidden" name="hid" :value="storeInfo.hid"/>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">管理二次验证</label>
                                <div class="col-sm-9">
                                    <input type="text" name="authcode" class="form-control" placeholder="请输入验证码">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="deleteHuoyuan()">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>
<script>
new Vue({
    el: "#orderlist",
    data: {
        row: null,
        storeInfo: {}
    },
    methods: {
        get: function(page) {
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=huoyuanlist", {page: page}, {emulateJSON: true}).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.row = data.body;
                } else {
                    layer.msg(data.data.msg, {icon: 2});
                }
            });
        },
        form: function(form) {
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=uphuoyuan", {data: $("#form-" + form).serialize()}, {emulateJSON: true}).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.get(this.row.current_page);
                    $("#modal-" + form).modal('hide');
                    layer.msg(data.data.msg, {icon: 1});
                } else {
                    layer.msg(data.data.msg, {icon: 2});
                }
            });
        },
        deleteHuoyuan: function() {
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=huoyuandel", {data: $("#form-delete").serialize()}, {emulateJSON: true}).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.get(this.row.current_page);
                    $("#modal-delete").modal('hide');
                    layer.msg(data.data.msg, {icon: 1});
                } else {
                    layer.msg(data.data.msg, {icon: 2});
                }
            });
        },
        bs: function(oid) {
            layer.msg(oid);
        }
    },
    mounted() {
        this.get(1);
    }
});
</script>