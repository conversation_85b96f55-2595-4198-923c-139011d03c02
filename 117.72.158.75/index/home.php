<?php
include('head.php'); 
?>
<link rel="stylesheet" href="../assets/yqsladmin/css/home.css" type="text/css" />
<div class="app">
<div class="layui-row layui-col-space15">
    <!-- 数据展示 -->
    <div class="el-row content-box">
        <div class="el-col el-col-md-6">
            <div class="dashboard-card bg-teal">
                <div class="dashboard-content">
                    <div class="dashboard-value">
                        <?php
                        if ($userrow['uid'] != "1") {
                            $sql = "SELECT COUNT(*) FROM qingka_wangke_order WHERE addtime>? AND uid=?";
                            $params = [$jtdate, $userrow['uid']];
                        } else {
                            $sql = "SELECT COUNT(*) FROM qingka_wangke_order WHERE addtime>?";
                            $params = [$jtdate];
                        }
                        $count = $DB->prepare_count($sql, $params);
                        echo $count;
                        ?>
                    </div>
                    <div class="dashboard-label">今日新增</div>
                </div>
                <div>
                    <i class="el-icon-circle-plus-outline dashboard-icon"></i>
                </div>
            </div>
        </div>
        <div class="el-col el-col-md-6">
            <div class="dashboard-card bg-blue">
                <div class="dashboard-content">
                    <div class="dashboard-value">
                        <?php
                        if ($userrow['uid'] != "1") {
                            $sql = "SELECT COUNT(*) FROM qingka_wangke_order WHERE uid = ?";
                            $params = [$userrow['uid']];
                        } else {
                            $sql = "SELECT COUNT(*) FROM qingka_wangke_order";
                            $params = [];
                        }
                        $count = $DB->prepare_count($sql, $params);
                        echo $count;
                        ?>
                    </div>
                    <div class="dashboard-label">所有订单</div>
                </div>
                <div>
                    <i class="el-icon-document dashboard-icon"></i>
                </div>
            </div>
        </div>
        <div class="el-col el-col-md-6">
            <div class="dashboard-card bg-orange">
                <div class="dashboard-content">
                    <div class="dashboard-value">
                        <?php echo $userrow['money']; ?>
                    </div>
                    <div class="dashboard-label">豆子数</div>
                </div>
                <div>
                    <i class="el-icon-money dashboard-icon"></i>
                </div>
            </div>
        </div>
        <div class="el-col el-col-md-6">
            <div class="dashboard-card bg-indigo">
                <div class="dashboard-content">
                    <div class="dashboard-value">
                        <?php
                        if ($userrow['uid'] != "1") {
                            $uid = $userrow['uid'];
                            $online = $DB->prepare_count("SELECT COUNT(*) FROM qingka_wangke_user WHERE endtime>? AND uuid=?", [$jtdate, $uid]);
                            $total = $DB->prepare_count("SELECT COUNT(*) FROM qingka_wangke_user WHERE uid=?", [$uid]);
                            echo "$online/$total";
                        } else {
                            $online = $DB->prepare_count("SELECT COUNT(*) FROM qingka_wangke_user WHERE endtime>?", [$jtdate]);
                            $total = $DB->prepare_count("SELECT COUNT(*) FROM qingka_wangke_user");
                            echo "$online/$total";
                        }
                        ?>
                    </div>
                    <div class="dashboard-label">我的代理</div>
                </div>
                <div>
                    <i class="el-icon-user dashboard-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <!-- 快捷操作 -->
    <div class="el-row content-box">
        <div class="el-col el-col-md-6">
            <button class="quick-action-button" onclick="openPage('add1', '交单')">
                <i class="el-icon-upload quick-action-icon icon-orange"></i>
                <div class="quick-action-label">交单</div>
            </button>
        </div>
        <div class="el-col el-col-md-6">
            <button class="quick-action-button" onclick="openPage('list', '订单')">
                <i class="el-icon-tickets quick-action-icon icon-purple"></i>
                <div class="quick-action-label">订单</div>
            </button>
        </div>
        <div class="el-col el-col-md-6">
            <button class="quick-action-button" onclick="openPage('pay', '充值')">
                <i class="el-icon-shopping-cart-2 quick-action-icon icon-pink"></i>
                <div class="quick-action-label">充值</div>
            </button>
        </div>
        <div class="el-col el-col-md-6">
            <button class="quick-action-button" onclick="openPage('userlist', '代理')">
                <i class="el-icon-user quick-action-icon icon-blue"></i>
                <div class="quick-action-label">代理</div>
            </button>
        </div>
    </div>
    <!-- 公告 -->
    <div class="el-row">
        <div class="el-col el-col-24">
            <div v-loading="loading" element-loading-text="加载中...">
                <el-card v-for="item in notices" :key="item.id" class="notice-card" :class="{ zhiding: item.zhiding }">
                    <div slot="header" class="clearfix">
                        <i class="el-icon-bell bell-icon"></i>
                        <span v-if="item.zhiding" class="zhiding-label"><i class="el-icon-paperclip"></i>置顶</span>
                        <span>{{ item.title }}</span>
                    </div>
                    <div>{{ item.content }}</div>
                    <div class="notice-time-source">
                        {{ item.time }}
                        <span v-if="item.shenfen === '全站通知'" class="notification-type quanzhan">{{ item.shenfen }}</span>
                        <span v-if="item.shenfen === '上级通知'" class="notification-type shangji">{{ item.shenfen }}</span>
                    </div>
                </el-card>
                <el-button v-if="hasMore" type="primary" class="more-button" @click="loadMore">加载更多</el-button>
            </div>
        </div>
    </div>
</div>
</div>
<script src="../assets/js/vue.min.js"></script>
<script src="../assets/js/axios.js"></script>
<script src="../assets/js/index.js"></script>
<script>
    function openPage(page, name) {
        parent.sp.showTab({ name: name, url: `${page}.php` });
    }
    new Vue({
        el: '.app',
        data: {
            notices: [],
            loading: false,
            page: 1,
            pageSize: 3,
            hasMore: true
        },
        mounted() {
            this.loadNotices();
        },
        methods: {
            loadNotices() {
                this.loading = true;
                axios.get('/apisub.php?act=showgonggao')
                    .then(response => {
                        if (response.data.code === 1) {
                            const newNotices = response.data.data.slice(0, this.page * this.pageSize);
                            this.notices = newNotices;
                            this.hasMore = response.data.data.length > this.page * this.pageSize;
                        } else {
                            this.$message.error('加载公告失败');
                        }
                    })
                    .catch(error => {
                        this.$message.error('请求失败，请稍后重试');
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },
            loadMore() {
                this.page++;
                this.loadNotices();
            }
        }
    });
</script>
</body>
</html>