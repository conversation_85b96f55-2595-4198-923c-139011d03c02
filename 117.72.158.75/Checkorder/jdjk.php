<?php

function processCx($oid)
{
	global $DB;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,user,pass from qingka_wangke_order where oid='{$oid}' ");
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$b["hid"]}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$user = $b["user"];
	$pass = $b["pass"];
	$kcname = $d["kcname"];
	$school = $d["school"];
	$pt = $d["noun"];
	$kcid = $d["kcid"];

	// yyy教育订单状态查询
	if ($type == "yyy") {
		// 先登录获取token
		$login_data = array(
			"username" => $a["user"],
			"password" => $a["pass"]
		);
		$login_url = $a["url"] . "/api/login";
		$headers = array(
			"Content-Type: application/json",
			"Accept: application/json, text/plain, */*",
			"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"X-Requested-With: XMLHttpRequest"
		);
		$login_result = get_url3($login_url, json_encode($login_data), "", $headers);
		$login_response = json_decode($login_result, true);

		if ($login_response['code'] != 200) {
			return array(array("code" => -1, "msg" => "登录失败: " . $login_response['message']));
		}

		$access_token = $login_response['data']['accessToken'];
		$refresh_token = $login_response['data']['refreshToken'];

		// 构建Cookie
		$token_data = array(
			"accessToken" => $access_token,
			"expires" => 1919520000000,
			"refreshToken" => $refresh_token
		);
		$token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
		$encoded_token = urlencode($token_json);
		$cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";

		// 查询订单列表
		$order_query_data = array(
			"lastoid" => "",
			"odname" => $user, // 使用账号名查询
			"nickname" => "",
			"notetype" => "1",
			"note" => "",
			"statusbox" => array(),
			"page" => 1,
			"pageSize" => 50
		);

		$query_headers = array(
			"Content-Type: application/json",
			"Accept: application/json, text/plain, */*",
			"Authorization: Bearer " . $access_token,
			"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"X-Requested-With: XMLHttpRequest"
		);

		$query_url = $a["url"] . "/api/getorder";
		$query_result = get_url3($query_url, json_encode($order_query_data), $cookie, $query_headers);
		$query_response = json_decode($query_result, true);

		if ($query_response['code'] != 200) {
			return array(array("code" => -1, "msg" => "查询订单失败: " . ($query_response['message'] ?? "未知错误")));
		}

		$orders = array();
		if (isset($query_response['data']['records']) && is_array($query_response['data']['records'])) {
			foreach ($query_response['data']['records'] as $order) {
				// 根据yyy教育的订单数据结构解析
				$orders[] = array(
					"code" => 1,
					"msg" => "查询成功",
					"yid" => $order['oid'] ?? '',
					"kcname" => $order['odname'] ?? $kcname,
					"user" => $user,
					"pass" => $pass,
					"ksks" => '',
					"ksjs" => '',
					"status_text" => $order['status'] ?? '未知状态',
					"process" => $order['progress'] ?? '0%',
					"remarks" => $order['note'] ?? ''
				);
			}
		}

		if (empty($orders)) {
			return array(array("code" => -1, "msg" => "未找到相关订单"));
		}

		return $orders;
	}

    if ($type == "29") {
        $data = array("username" => $user);
        $dx_rl = $a["url"];
        $dx_url = "$dx_rl/api.php?act=chadan";
        $result = get_url($dx_url, $data);
        $result = json_decode($result, true);
        if ($result["code"] == "1") {
        foreach ($result["data"] as $res) {
        $yid = $res["id"];
        $kcname = $res["kcname"];
        $status = $res["status"];
        $process = $res["process"];
        $remarks = $res["remarks"];
        $kcks = $res["courseStartTime"];
        $kcjs = $res["courseEndTime"];
        $ksks = $res["examStartTime"];
        $ksjs = $res["examEndTime"];
        $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
        }
        } else {
        $b[] = array("code" => -1, "msg" => $result["msg"]);
        }
        return $b;
    }
     if ($type == "bdkj") {
       $data = array("oid" => $d['yid'],"uid" => $a["user"], "key" => $a["pass"]);
    $dx_rl = $a["url"];
    $dx_url  = "$dx_rl/api.php?act=chadanoid";
    $result = get_url($dx_url, $data);
    $result = json_decode($result, true);
    if ($result["code"] == "1") {
    foreach ($result["data"] as $res) {
    $yid = $res["id"];
    $kcname = $res["kcname"];
    $status = $res["status"];
    $process = $res["process"];
    $remarks = $res["remarks"];
    $kcks = $res["courseStartTime"];
    $kcjs = $res["courseEndTime"];
    $ksks = $res["examStartTime"];
    $ksjs = $res["examEndTime"];
    $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
    }
    } else {
    $b[] = array("code" => -1, "msg" => "查询失败,请联系管理员");
    }
    return $b;
    }
	else {
       $b[] = array("code" => -1, "msg" => "查询失败,请联系管理员"); 
	}
	
	
	  
	
}

?>