-- 管理员操作日志表
CREATE TABLE IF NOT EXISTS `qingka_wangke_admin_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_uid` int(11) NOT NULL COMMENT '管理员UID',
  `admin_user` varchar(50) NOT NULL COMMENT '管理员用户名',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `details` text COMMENT '操作详情',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `data` text COMMENT '相关数据JSON',
  `addtime` datetime NOT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_uid` (`admin_uid`),
  KEY `idx_action` (`action`),
  KEY `idx_addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- 管理员权限表
CREATE TABLE IF NOT EXISTS `qingka_wangke_admin_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL COMMENT '用户UID',
  `permission` varchar(100) NOT NULL COMMENT '权限名称',
  `granted_by` int(11) NOT NULL COMMENT '授权人UID',
  `granted_at` datetime NOT NULL COMMENT '授权时间',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=有效，0=无效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid_permission` (`uid`, `permission`),
  KEY `idx_uid` (`uid`),
  KEY `idx_permission` (`permission`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员权限表';

-- 安全配置表
CREATE TABLE IF NOT EXISTS `qingka_wangke_security_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) COMMENT '配置描述',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人UID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全配置表';

-- 插入默认安全配置
INSERT INTO `qingka_wangke_security_config` (`config_key`, `config_value`, `description`) VALUES
('admin_session_timeout', '3600', '管理员会话超时时间（秒）'),
('max_login_attempts', '5', '最大登录尝试次数'),
('login_lockout_time', '900', '登录锁定时间（秒）'),
('api_rate_limit_per_minute', '60', 'API每分钟调用限制'),
('api_rate_limit_per_hour', '1000', 'API每小时调用限制'),
('enable_two_factor_auth', '0', '是否启用双因素认证'),
('password_min_length', '8', '密码最小长度'),
('password_require_special_chars', '1', '密码是否需要特殊字符'),
('enable_ip_whitelist', '0', '是否启用IP白名单'),
('log_retention_days', '90', '日志保留天数');

-- 登录失败记录表
CREATE TABLE IF NOT EXISTS `qingka_wangke_login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `attempt_time` datetime NOT NULL COMMENT '尝试时间',
  `success` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否成功：1=成功，0=失败',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip` (`ip`),
  KEY `idx_attempt_time` (`attempt_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录尝试记录表';

-- IP白名单表
CREATE TABLE IF NOT EXISTS `qingka_wangke_ip_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `ip_range` varchar(100) DEFAULT NULL COMMENT 'IP范围（CIDR格式）',
  `description` varchar(255) COMMENT '描述',
  `created_by` int(11) NOT NULL COMMENT '创建人UID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=有效，0=无效',
  PRIMARY KEY (`id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP白名单表';
