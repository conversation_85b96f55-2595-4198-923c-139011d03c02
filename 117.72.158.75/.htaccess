# 安全配置文件
# 防止访问敏感文件和目录

# 拒绝访问配置文件目录
<DirectoryMatch "^.*(confing|Checkorder|redis).*$">
    Require all denied
</DirectoryMatch>

# 拒绝访问日志文件
<FilesMatch "\.(log|txt)$">
    Require all denied
</FilesMatch>

# 拒绝访问备份文件
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Require all denied
</FilesMatch>

# 防止目录浏览
Options -Indexes

# 安全头部
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# 限制文件上传大小
LimitRequestBody 10485760