/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableCookie={exports:{}}.exports}})(this,function(){'use strict';function a(a,b){if(!(a instanceof b))throw new TypeError('Cannot call a class as a function')}function b(a,b){if(!a)throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called');return b&&('object'==typeof b||'function'==typeof b)?b:a}function c(a,b){if('function'!=typeof b&&null!==b)throw new TypeError('Super expression must either be null or a function, not '+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}var d=function(){function a(a,b){for(var c,d=0;d<b.length;d++)c=b[d],c.enumerable=c.enumerable||!1,c.configurable=!0,'value'in c&&(c.writable=!0),Object.defineProperty(a,c.key,c)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=function a(b,c,d){null===b&&(b=Function.prototype);var e=Object.getOwnPropertyDescriptor(b,c);if(e===void 0){var f=Object.getPrototypeOf(b);return null===f?void 0:a(f,c,d)}if('value'in e)return e.value;var g=e.get;return void 0===g?void 0:g.call(d)};(function(f){var g={cookieIds:{sortOrder:'bs.table.sortOrder',sortName:'bs.table.sortName',pageNumber:'bs.table.pageNumber',pageList:'bs.table.pageList',columns:'bs.table.columns',searchText:'bs.table.searchText',filterControl:'bs.table.filterControl',filterBy:'bs.table.filterBy'},getCurrentHeader:function(a){var b=a.$header;return a.options.height&&(b=a.$tableHeader),b},getCurrentSearchControls:function(a){var b='select, input';return a.options.height&&(b='table select, table input'),b},cookieEnabled:function(){return!!navigator.cookieEnabled},inArrayCookiesEnabled:function(a,b){for(var c=-1,d=0;d<b.length;d++)if(a.toLowerCase()===b[d].toLowerCase()){c=d;break}return c},setCookie:function(a,b,c){if(a.options.cookie&&g.cookieEnabled()&&''!==a.options.cookieIdTable&&-1!==g.inArrayCookiesEnabled(b,a.options.cookiesEnabled)){switch(b=a.options.cookieIdTable+'.'+b,a.options.cookieStorage){case'cookieStorage':document.cookie=[b,'=',encodeURIComponent(c),'; expires='+g.calculateExpiration(a.options.cookieExpire),a.options.cookiePath?'; path='+a.options.cookiePath:'',a.options.cookieDomain?'; domain='+a.options.cookieDomain:'',a.options.cookieSecure?'; secure':''].join('');break;case'localStorage':localStorage.setItem(b,c);break;case'sessionStorage':sessionStorage.setItem(b,c);break;default:return!1;}return!0}},getCookie:function(a,b,c){if(!c)return null;if(-1===g.inArrayCookiesEnabled(c,a.options.cookiesEnabled))return null;switch(c=b+'.'+c,a.options.cookieStorage){case'cookieStorage':var d='; '+document.cookie,e=d.split('; '+c+'=');return 2===e.length?decodeURIComponent(e.pop().split(';').shift()):null;case'localStorage':return localStorage.getItem(c);case'sessionStorage':return sessionStorage.getItem(c);default:return null;}},deleteCookie:function(a,b,c){switch(c=b+'.'+c,a.options.cookieStorage){case'cookieStorage':document.cookie=[encodeURIComponent(c),'=','; expires=Thu, 01 Jan 1970 00:00:00 GMT',a.options.cookiePath?'; path='+a.options.cookiePath:'',a.options.cookieDomain?'; domain='+a.options.cookieDomain:''].join('');break;case'localStorage':localStorage.removeItem(c);break;case'sessionStorage':sessionStorage.removeItem(c);break;default:return!1;}return!0},calculateExpiration:function(a){var b=a.replace(/[0-9]*/,'');switch(a=a.replace(/[A-Za-z]{1,2}/,''),b.toLowerCase()){case's':a=+a;break;case'mi':a*=60;break;case'h':a=60*(60*a);break;case'd':a=60*(60*(24*a));break;case'm':a=60*(60*(24*(30*a)));break;case'y':a=60*(60*(24*(365*a)));break;default:a=void 0;}if(!a)return'';var c=new Date;return c.setTime(c.getTime()+1e3*a),c.toGMTString()},initCookieFilters:function(a){setTimeout(function(){var b=JSON.parse(g.getCookie(a,a.options.cookieIdTable,g.cookieIds.filterControl));if(!a.options.filterControlValuesLoaded&&b){var c={},d=g.getCurrentHeader(a),e=g.getCurrentSearchControls(a),h=function(a,b){f(b).each(function(b,d){''!==d.text&&(f(a).val(d.text),c[d.field]=d.text)})};d.find(e).each(function(){var a=f(this).closest('[data-field]').data('field'),c=b.filter(function(b){return b.field===a});h(this,c)}),a.initColumnSearch(c),a.options.filterControlValuesLoaded=!0,a.initServer()}},250)}};f.extend(f.fn.bootstrapTable.defaults,{cookie:!1,cookieExpire:'2h',cookiePath:null,cookieDomain:null,cookieSecure:null,cookieIdTable:'',cookiesEnabled:['bs.table.sortOrder','bs.table.sortName','bs.table.pageNumber','bs.table.pageList','bs.table.columns','bs.table.searchText','bs.table.filterControl','bs.table.filterBy'],cookieStorage:'cookieStorage',filterControls:[],filterControlValuesLoaded:!1}),f.fn.bootstrapTable.methods.push('getCookies'),f.fn.bootstrapTable.methods.push('deleteCookie'),f.extend(f.fn.bootstrapTable.utils,{setCookie:g.setCookie,getCookie:g.getCookie}),f.BootstrapTable=function(h){function i(){return a(this,i),b(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return c(i,h),d(i,[{key:'init',value:function(){var a=JSON.parse(g.getCookie(this,this.options.cookieIdTable,g.cookieIds.filterBy));if(this.filterColumns=a?a:{},this.options.filterControls=[],this.options.filterControlValuesLoaded=!1,this.options.cookiesEnabled='string'==typeof this.options.cookiesEnabled?this.options.cookiesEnabled.replace('[','').replace(']','').replace(/ /g,'').toLowerCase().split(','):this.options.cookiesEnabled,this.options.filterControl){var b=this;this.$el.on('column-search.bs.table',function(a,c,d){for(var e=!0,f=0;f<b.options.filterControls.length;f++)if(b.options.filterControls[f].field===c){b.options.filterControls[f].text=d,e=!1;break}e&&b.options.filterControls.push({field:c,text:d}),g.setCookie(b,g.cookieIds.filterControl,JSON.stringify(b.options.filterControls))}).on('created-controls.bs.table',g.initCookieFilters(b))}e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'init',this).call(this)}},{key:'initServer',value:function(){var a;if(this.options.cookie&&this.options.filterControl&&!this.options.filterControlValuesLoaded){var f=JSON.parse(g.getCookie(this,this.options.cookieIdTable,g.cookieIds.filterControl));if(f)return}for(var b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'initServer',this)).call.apply(a,[this].concat(c))}},{key:'initTable',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'initTable',this)).call.apply(a,[this].concat(c)),this.initCookie()}},{key:'onSort',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'onSort',this)).call.apply(a,[this].concat(c)),g.setCookie(this,g.cookieIds.sortOrder,this.options.sortOrder),g.setCookie(this,g.cookieIds.sortName,this.options.sortName)}},{key:'onPageNumber',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'onPageNumber',this)).call.apply(a,[this].concat(c)),g.setCookie(this,g.cookieIds.pageNumber,this.options.pageNumber)}},{key:'onPageListChange',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'onPageListChange',this)).call.apply(a,[this].concat(c)),g.setCookie(this,g.cookieIds.pageList,this.options.pageSize),g.setCookie(this,g.cookieIds.pageNumber,this.options.pageNumber)}},{key:'onPagePre',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'onPagePre',this)).call.apply(a,[this].concat(c)),g.setCookie(this,g.cookieIds.pageNumber,this.options.pageNumber)}},{key:'onPageNext',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'onPageNext',this)).call.apply(a,[this].concat(c)),g.setCookie(this,g.cookieIds.pageNumber,this.options.pageNumber)}},{key:'toggleColumn',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'toggleColumn',this)).call.apply(a,[this].concat(c));var h=[];f.each(this.columns,function(a,b){b.visible&&h.push(b.field)}),g.setCookie(this,g.cookieIds.columns,JSON.stringify(h))}},{key:'selectPage',value:function(a){e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'selectPage',this).call(this,a),g.setCookie(this,g.cookieIds.pageNumber,a)}},{key:'onSearch',value:function(a){e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'onSearch',this).call(this,a),f(a.currentTarget).parent().hasClass('search')&&g.setCookie(this,g.cookieIds.searchText,this.searchText),g.setCookie(this,g.cookieIds.pageNumber,this.options.pageNumber)}},{key:'filterBy',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'filterBy',this)).call.apply(a,[this].concat(c)),g.setCookie(this,g.cookieIds.filterBy,JSON.stringify(this.filterColumns))}},{key:'initCookie',value:function(){if(this.options.cookie){if(''===this.options.cookieIdTable||''===this.options.cookieExpire||!g.cookieEnabled())return console.error('Configuration error. Please review the cookieIdTable and the cookieExpire property. If the properties are correct, then this browser does not support cookies.'),void(this.options.cookie=!1);var a=g.getCookie(this,this.options.cookieIdTable,g.cookieIds.sortOrder),b=g.getCookie(this,this.options.cookieIdTable,g.cookieIds.sortName),c=g.getCookie(this,this.options.cookieIdTable,g.cookieIds.pageNumber),d=g.getCookie(this,this.options.cookieIdTable,g.cookieIds.pageList),e=JSON.parse(g.getCookie(this,this.options.cookieIdTable,g.cookieIds.columns)),h=g.getCookie(this,this.options.cookieIdTable,g.cookieIds.searchText);this.options.sortOrder=a?a:this.options.sortOrder,this.options.sortName=b?b:this.options.sortName,this.options.pageNumber=c?+c:this.options.pageNumber,this.options.pageSize=d?d===this.options.formatAllRows()?d:+d:this.options.pageSize,this.options.searchText=h?h:'',e&&f.each(this.columns,function(a,b){b.visible=-1!==f.inArray(b.field,e)})}}},{key:'getCookies',value:function(){var a=this,b={};return f.each(g.cookieIds,function(c,d){b[c]=g.getCookie(a,a.options.cookieIdTable,d),'columns'===c&&(b[c]=JSON.parse(b[c]))}),b}},{key:'deleteCookie',value:function(a){''!==a&&g.cookieEnabled()&&g.deleteCookie(this,this.options.cookieIdTable,g.cookieIds[a])}}]),i}(f.BootstrapTable)})(jQuery)});