<?php
$mod='blank';
$title='最新上架';
require_once('head.php');
?>
	<div class="app-content-body">
	    <div class="wrapper-md control">		
			<div class="row">
				<div class="col-sm-12">
				    <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
				    <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">【商品最新上架】 此处不显示密价！ 如有密价请以实际为准！ （Ctrl+F搜索）<br> 【CID就是查询参数和对接参数】</div>
          <div class="card-body">
              <div class="row">
                        <div class="col-xs-4 col-sm-2">
                          <button id="exportBtn" class="btn btn-info btn-round" style="margin-left: 14px; width: 100%;" >导出最新上架</button>
                            </div>
                        </div>
            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th scope="col">上架时间</th>
                    <th scope="col">分类ID</th>
                    <th scope="col">CID</th>
                    <th scope="col">课程名称</th>
                    <th scope="col">我的价格</th>
                  </tr>
                </thead>
                <tbody>
                  <?php 
                    $a=$DB->query("select * from qingka_wangke_class where status=1 order by cid desc limit 100");
                    while($rs=$DB->fetch($a)){
                      echo "<tr>
                        <td>".$rs['addtime']."</td>
                        <td>".$rs['fenlei']."</td>
                        <td>".$rs['cid']."</td>
                        <td>".$rs['name']."</td>
                        <td>".($rs['price']*$userrow['addprice'])."</td></tr>"; 
                    }
                  ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
<?php require_once("lightyearfooter.php");?> 
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    $('#exportBtn').click(function() {
        var fileName = "最新上架项目.xlsx";
        var table = document.querySelector('.table'); 
        var wb = XLSX.utils.table_to_book(table, {sheet:"最新上架"});
        XLSX.writeFile(wb, fileName);
    });
});
</script>